#include "ZGSTHandle.h"
#include "ZGProxyCommon.h"
#include "ZGProxyMng.h"
#include "ZGWebModule.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include <QJsonDocument>
#include <QJsonArray>

ZGSTHandle::ZGSTHandle(QObject* parent) : QObject(parent)
{
}

ZGWebModule::Response ZGSTHandle::on_st_stations_get(const QString&/*clientID*/, const QVariantMap& headers, const QJsonValue&/*param*/, const QHttpServerRequest&/*req*/)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
	{
		ZG6000::ListStringMap listMapStation;
		ZG6000::ErrorInfo e;
		if (!stProxy->getValidStations(listMapStation, e))
			return ZGWebModule::errorObject(e.errDetail.c_str());
        const auto& json = ZGJson::convertToJson(listMapStation);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSTHandle::on_st_mc_stations_get(const QString &clientID, const QVariantMap& headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString sql = QString("SELECT DISTINCT a.id, a.name FROM sp_param_appnode a LEFT JOIN mp_param_device b ON a.id = b.appNodeID "
                          "WHERE b.isEnable = '1' AND b.subtypeID = 'ZG_DS_MCS'");
    ZG6000::ListStringMap listStation;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listStation))
        return ZGWebModule::errorObject(QStringLiteral("获取站点信息失败"));
    const auto& json = ZGJson::convertToJson(listStation);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGSTHandle::on_st_station_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest&/*req*/)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
	{
		QJsonArray array = param.toArray();
		QString inStation = array[0].toString();
		QString outStation = array[1].toString();
		ZG6000::ErrorInfo e;
		if (!stProxy->setMeasureStation(clientID.toStdString(), inStation.toStdString(), outStation.toStdString(), e))
			return ZGWebModule::errorObject(e.errDetail.c_str());
		return ZGWebModule::replyObject("");
	});
}

ZGWebModule::Response ZGSTHandle::on_st_offset_calc(const QString& /*clientID*/, const QVariantMap& headers, const QJsonValue&/*param*/, const QHttpServerRequest&/*req*/)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
	{
		ZG6000::ErrorInfo e;
		if (!stProxy->calculateOffset(e))
			return ZGWebModule::errorObject(e.errDetail.c_str());
		return ZGWebModule::replyObject("");
	});
}

ZGWebModule::Response ZGSTHandle::on_st_calc_start(const QString& /*clientID*/, const QVariantMap& headers, const QJsonValue&/*param*/, const QHttpServerRequest&/*req*/)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
	{
		ZG6000::ErrorInfo e;
		if (!stProxy->startCalculate(e))
			return ZGWebModule::errorObject(e.errDetail.c_str());
		return ZGWebModule::replyObject("");
	});
}

ZGWebModule::Response ZGSTHandle::on_st_calc_stop(const QString& /*clientID*/, const QVariantMap& headers, const QJsonValue&/*param*/, const QHttpServerRequest&/*req*/)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
	{
		ZG6000::ErrorInfo e;
		if (!stProxy->stopCalculate(e))
			return ZGWebModule::errorObject(e.errDetail.c_str());
		return ZGWebModule::replyObject("");
	});
}

ZGWebModule::Response ZGSTHandle::on_st_devices_get(const QString&/*clientID*/, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest&/*req*/)
{
	const auto& appNodeID = param.toString();
	QString sql = QString("SELECT a.appNodeID AS id, b.name FROM sp_param_appnode_layer a LEFT JOIN sp_param_appnode b ON a.appNodeID = b.id "
		"WHERE parentAppNodeID = '%1' ORDER BY itemIndex").arg(appNodeID);
	ZG6000::ListStringMap listAppNode;
	if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAppNode))
		return ZGWebModule::errorObject(QStringLiteral("获取站点'%1'子站信息失败").arg(appNodeID));
	if (listAppNode.empty())
	{
		sql = QString("SELECT name FROM sp_param_appnode WHERE id = '%1'").arg(appNodeID);
		std::string name;
		if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), name))
			return ZGWebModule::errorObject(QStringLiteral("获取站点'%1'信息失败").arg(appNodeID));
		listAppNode.push_back({{"id", appNodeID.toStdString()}, {"name", name}});
	}
	try
	{
		QJsonArray nodeArray;
		for (const auto& appNode : listAppNode)
		{
			const auto& subAppNodeID = ZGUtils::get(appNode, "id");
			QJsonObject nodeObj;
			nodeObj["id"] = ZGUtils::get(appNode, "id").c_str();
			nodeObj["name"] = ZGUtils::get(appNode, "name").c_str();
            sql = QString("SELECT COUNT(*) FROM mp_param_device WHERE appNodeID = '%1' AND (subtypeID = 'ZG_DS_SENS' OR typeID = 'ZG_DS_MCS')")
				.arg(subAppNodeID.c_str());
			std::string strCount;
			if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), strCount))
				return ZGWebModule::errorObject(QStringLiteral("获取站点'%1'传感器数量失败"));
			if (std::atoi(strCount.c_str()) == 0)
				continue;
            sql = QString("SELECT id, name, typeID, subtypeID FROM mp_param_device WHERE appNodeID = '%1' AND subsystemID = 'ZG_SS_ZS' "
                "AND majorID = 'ZG_MJ_ZS' AND isEnable = '1'").arg(subAppNodeID.c_str());
			ZG6000::ListStringMap listDevice;
			if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
				return ZGWebModule::errorObject(QStringLiteral("获取站点'%1'设备失败").arg(subAppNodeID.c_str()));
			QJsonArray deviceArray;
			for (const auto& device : listDevice)
			{
				QJsonObject obj;
				const auto& deviceID = ZGUtils::get(device, "id");
                const auto& subtypeID = ZGUtils::get(device, "subtypeID");
				obj["id"] = deviceID.c_str();
				obj["name"] = ZGUtils::get(device, "name").c_str();
                obj["typeID"] = ZGUtils::get(device, "typeID").c_str();
                obj["subtypeID"] = subtypeID.c_str();
                if (subtypeID == "ZG_DS_MCS" || subtypeID == "ZG_DS_SENS")
				{
					std::string direction;
                    ZG6000::ErrorInfo e;
                    if (!ZGProxyCommon::getPropertyValue(deviceID, "Direction", direction, e))
                        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
					obj["direction"] = direction.c_str();
				}
				deviceArray.append(obj);
			}
			nodeObj["devices"] = deviceArray;
			nodeArray.append(nodeObj);
		}
		return ZGWebModule::replyObject(nodeArray);
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
	catch (const std::exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
}

ZGWebModule::Response ZGSTHandle::on_st_device_relation_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req)
{
	const auto& deviceObj = param.toObject();
	if (deviceObj.empty())
		return ZGWebModule::replyObject("");
	ZG6000::StringList listSql;
	for (auto it = deviceObj.constBegin(); it != deviceObj.end(); ++it)
	{
		const auto& deviceID = it.key();
		listSql.push_back(QString("DELETE FROM mp_param_device_relation WHERE srcDeviceID = '%1' AND relationTypeID = 'ZG_RT_ASSOC';")
		                  .arg(deviceID).toStdString());
		auto assocDevices = it.value();
		const auto& assocDeviceArray = assocDevices.toArray();
        ZG6000::StringList listID;
        if (!ZGProxyCommon::createUUID(assocDeviceArray.size(), listID))
            return ZGWebModule::replyObject(QStringLiteral("创建记录ID失败"));
        int index = 0;
		for (auto assocDevice : assocDeviceArray)
		{
			const auto& dstDeviceID = assocDevice.toString().toStdString();
			listSql.push_back(ZGUtils::generateInsertSql("mp_param_device_relation", {
                {"id", listID[index++]}, {"srcDeviceID", deviceID.toStdString()},
				{"dstDeviceID", dstDeviceID}, {"relationTypeID", "ZG_RT_ASSOC"}
			}));
		}
	}
	if (!ZGProxyCommon::execBatchSql(listSql))
		return ZGWebModule::errorObject(QStringLiteral("更新排流支路与传感器关系失败"));
	return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSTHandle::on_st_device_param_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req)
{
	const auto& condition = param.toString();
	QString sql = QString("SELECT a.id, a.name, a.appNodeID, b.name AS appNodeName FROM mp_param_device a LEFT JOIN sp_param_appnode b "
		"ON a.appNodeID = b.id WHERE %1").arg(condition);
	ZG6000::ListStringMap listResult;
	if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listResult))
		return ZGWebModule::errorObject(QStringLiteral("获取设备参数失败"));
	const auto& json = ZGJson::convertToJson(listResult);
	QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
	return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGSTHandle::on_st_plzl_assoc_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req)
{
	const auto& plzlID = param.toString();
	QString sql = QString("SELECT a.dstDeviceID AS id, b.name, b.appNodeID, c.name AS appNodeName FROM mp_param_device_relation a LEFT JOIN mp_param_device b "
		"ON a.dstDeviceID = b.id LEFT JOIN sp_param_appnode c ON b.appNodeID = c.id WHERE a.srcDeviceID = '%1'").arg(plzlID);
	ZG6000::ListStringMap listResult;
	if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listResult))
		return ZGWebModule::errorObject(QStringLiteral("获取排流支路关系失败"));
	const auto& json = ZGJson::convertToJson(listResult);
	QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
	return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGSTHandle::on_st_system_param_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap systemParam;
        ZG6000::ErrorInfo e;
        if (!stProxy->getSystemParam(systemParam, e))
            return ZGWebModule::errorObject(e.errDetail.c_str());
        std::string json = ZGJson::convertToJson(systemParam);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.object());
    });
}

ZGWebModule::Response ZGSTHandle::on_st_system_param_set(const QString& clientID, const QVariantMap &headers, const QJsonValue& param, const QHttpServerRequest& req)
{
    return systemCall([&](std::shared_ptr<ZG6000::ZGSTStraySystemPrx> stProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        ZG6000::StringMap systemParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            systemParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!stProxy->setSystemParam(systemParam, e))
            return ZGWebModule::errorObject(e.errDetail.c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSTHandle::systemCall(std::function<ZGWebModule::Response (std::shared_ptr<ZG6000::ZGSTStraySystemPrx>)> func)
{
    auto proxy = ZGProxyMng::instance()->getProxySTStraySystem();
	if (proxy == nullptr)
        return ZGWebModule::errorObject("getProxySTStraySystem error.");
	try
	{
		return func(proxy);
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
	catch (const std::exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
}
