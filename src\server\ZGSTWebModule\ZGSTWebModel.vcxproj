﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A26059CA-49C4-3EA6-A7BB-FD4E80BE9BE9}</ProjectGuid>
    <RootNamespace>ZGSTWebModel</RootNamespace>
    <Keyword>QtVS_v304</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.19041.0</WindowsTargetPlatformMinVersion>
  <QtMsBuild Condition="'$(QtMsBuild)'=='' or !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild></PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v142</PlatformToolset>
    <OutputDirectory>..\..\..\lib\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <IntermediateDirectory>..\..\..\tmp\ZGSTWebModel\obj\</IntermediateDirectory>
    <PrimaryOutput>ZGSTWebModel</PrimaryOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v142</PlatformToolset>
    <OutputDirectory>..\..\..\lib\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <IntermediateDirectory>..\..\..\tmp\ZGSTWebModeld\obj\</IntermediateDirectory>
    <PrimaryOutput>ZGSTWebModeld</PrimaryOutput>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" /><Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') or !Exists('$(QtMsBuild)\qt.props')"><Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." /></Target>
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" /><ImportGroup Condition="Exists('$(QtMsBuild)\qt_defaults.props')"><Import Project="$(QtMsBuild)\qt_defaults.props" /></ImportGroup><PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"><OutDir>..\..\..\lib\</OutDir><IntDir>..\..\..\tmp\ZGSTWebModeld\obj\</IntDir><TargetName>ZGSTWebModeld</TargetName><IgnoreImportLibrary>true</IgnoreImportLibrary><PostBuildEventUseInBuild>true</PostBuildEventUseInBuild></PropertyGroup><PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'"><OutDir>..\..\..\lib\</OutDir><IntDir>..\..\..\tmp\ZGSTWebModel\obj\</IntDir><TargetName>ZGSTWebModel</TargetName><IgnoreImportLibrary>true</IgnoreImportLibrary><LinkIncremental>false</LinkIncremental><PostBuildEventUseInBuild>true</PostBuildEventUseInBuild></PropertyGroup><PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"><QtInstall>6.2.4_msvc2019_64</QtInstall><QtModules>core;gui;network;concurrent</QtModules></PropertyGroup><PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'"><QtInstall>6.2.4_msvc2019_64</QtInstall><QtModules>core;gui;network;concurrent</QtModules></PropertyGroup><ImportGroup Condition="Exists('$(QtMsBuild)\qt.props')"><Import Project="$(QtMsBuild)\qt.props" /></ImportGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice;..\..\..\tmp\ZGSTWebModel\moc;/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr /bigobj -utf-8 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>..\..\..\tmp\ZGSTWebModel\obj\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>None</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ObjectFileName>..\..\..\tmp\ZGSTWebModel\obj\</ObjectFileName>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;QT_PLUGIN;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <ProgramDataBaseFileName></ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
    <MultiProcessorCompilation>true</MultiProcessorCompilation></ClCompile>
    <Link>
      <AdditionalDependencies>D:\ZG\ZG6000\lib\ZGPubFun.lib;D:\ZG\ZG6000\lib\ZGRuntime.lib;D:\ZG\ZG6000\lib\ZGDebugMng.lib;D:\ZG\ZG6000\lib\ZGProxyMng.lib;D:\ZG\ZG6000\lib\ZGWebModule.lib;D:\ZG\ZG6000\lib\ZGJson.lib;E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib;D:\ZG\ZG6000\lib\Qt6HttpServer.lib;$(QTDIR)\lib\Qt6Gui.lib;$(QTDIR)\lib\Qt6Network.lib;$(QTDIR)\lib\Qt6Concurrent.lib;$(QTDIR)\lib\Qt6Core.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:\ZG\ZG6000\lib;D:\ZG\ZG6000\bin;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkDLL>true</LinkDLL>
      <LinkIncremental>false</LinkIncremental>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)\ZGSTWebModel.dll</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;QT_PLUGIN;QT_GUI_LIB;QT_NETWORK_LIB;QT_CONCURRENT_LIB;QT_CORE_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <PostBuildEvent>
      <Command>copy "$(TargetPath)" ..\..\..\bin\</Command>
      <Message>Copy ZGSTWebModel.dll to ..\..\..\bin\</Message>
    </PostBuildEvent>
  <QtMoc><CompilerFlavor>msvc</CompilerFlavor><Include>D:/ZG/ZG6000/tmp/ZGSTWebModel/moc/moc_predefs.h</Include><ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription><DynamicSource>output</DynamicSource><QtMocDir>D:\ZG\ZG6000\tmp\ZGSTWebModel\moc</QtMocDir><QtMocFileName>moc_%(Filename).cpp</QtMocFileName></QtMoc></ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice;..\..\..\tmp\ZGSTWebModeld\moc;/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr /bigobj -utf-8 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>..\..\..\tmp\ZGSTWebModeld\obj\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ObjectFileName>..\..\..\tmp\ZGSTWebModeld\obj\</ObjectFileName>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;QT_PLUGIN;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <WarningLevel>Level3</WarningLevel>
    <MultiProcessorCompilation>true</MultiProcessorCompilation></ClCompile>
    <Link>
      <AdditionalDependencies>ZGPubFund.lib;ZGRuntimed.lib;ZGDebugMngd.lib;ZGProxyMngd.lib;ZGWebModuled.lib;ZGJsond.lib;E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib;D:\ZG\ZG6000\lib\Qt6HttpServerd.lib;$(QTDIR)\lib\Qt6Guid.lib;$(QTDIR)\lib\Qt6Networkd.lib;$(QTDIR)\lib\Qt6Concurrentd.lib;$(QTDIR)\lib\Qt6Cored.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:\ZG\ZG6000\lib;D:\ZG\ZG6000\bin;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkDLL>true</LinkDLL>
      <OutputFile>$(OutDir)\ZGSTWebModeld.dll</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;QT_PLUGIN;QT_GUI_LIB;QT_NETWORK_LIB;QT_CONCURRENT_LIB;QT_CORE_LIB;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <PostBuildEvent>
      <Command>copy "$(TargetPath)" ..\..\..\bin\</Command>
      <Message>Copy ZGSTWebModeld.dll to ..\..\..\bin\</Message>
    </PostBuildEvent>
  <QtMoc><CompilerFlavor>msvc</CompilerFlavor><Include>D:/ZG/ZG6000/tmp/ZGSTWebModeld/moc/moc_predefs.h</Include><ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription><DynamicSource>output</DynamicSource><QtMocDir>D:\ZG\ZG6000\tmp\ZGSTWebModeld\moc</QtMocDir><QtMocFileName>moc_%(Filename).cpp</QtMocFileName></QtMoc></ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="ZGSTHandle.cpp" />
    <ClCompile Include="ZGSTWebModel.cpp" />
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="ZGSTHandle.h">
      
      
      
      
      
      
      
      
    </QtMoc>
    <QtMoc Include="ZGSTWebModel.h">
      
      
      
      
      
      
      
      
    </QtMoc>
  </ItemGroup>
  <ItemGroup>
    
    
    
    
    <CustomBuild Include="D:\ZG\ZG6000\tmp\ZGSTWebModel\moc\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr /bigobj -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGSTWebModel\moc\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGSTWebModel\moc\moc_predefs.h;%(Outputs)</Outputs>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
    </CustomBuild>
    <CustomBuild Include="D:\ZG\ZG6000\tmp\ZGSTWebModeld\moc\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr /bigobj -Zi -MDd -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGSTWebModeld\moc\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGSTWebModeld\moc\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="ZGSTWebModule.json" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" /><ImportGroup Condition="Exists('$(QtMsBuild)\qt.targets')"><Import Project="$(QtMsBuild)\qt.targets" /></ImportGroup>
  <ImportGroup Label="ExtensionTargets" />
</Project>