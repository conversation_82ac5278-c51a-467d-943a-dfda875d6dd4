#############################################################################
# Makefile for building: ZGSPWebServer
# Generated by qmake (3.1) (Qt 6.5.3)
# Project:  ZGSPWebServer.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_HTTPSERVER_LIB -DQT_WEBSOCKETS_LIB -DQT_NETWORK_LIB -DQT_CONCURRENT_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -O2 -Zi -MD -utf-8 -W3 -w44456 -w44457 -w44458 /Fd..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.vc.pdb $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -Zi -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc /Fd..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.vc.pdb $(DEFINES)
INCPATH       = -I. -IE:\Library\ice-3.7.6\include -I. -I..\..\include -I..\..\include\thirdparty -I..\..\ice -IE:\Qt\6.5.3\msvc2019_64\include -IE:\Qt\6.5.3\msvc2019_64\include\QtHttpServer -IE:\Qt\6.5.3\msvc2019_64\include\QtWebSockets -IE:\Qt\6.5.3\msvc2019_64\include\QtNetwork -IE:\Qt\6.5.3\msvc2019_64\include\QtConcurrent -IE:\Qt\6.5.3\msvc2019_64\include\QtCore -I..\..\..\tmp\ZGSPWebServer\moc -IE:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /OPT:REF /OPT:ICF /INCREMENTAL:NO /DEBUG /SUBSYSTEM:WINDOWS "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"
LIBS          = /LIBPATH:D:\ZG\ZG6000\lib /LIBPATH:E:\Library\ice-3.7.6\lib\x64\Release /LIBPATH:E:\Library\ice-3.7.6\lib\x64\Debug D:\ZG\ZG6000\lib\ZGRuntime.lib D:\ZG\ZG6000\lib\ZGPubFun.lib D:\ZG\ZG6000\lib\ZGServerBase.lib D:\ZG\ZG6000\lib\ZGRedisClient.lib D:\ZG\ZG6000\lib\ZGJson.lib D:\ZG\ZG6000\lib\ZGServerApplication.lib D:\ZG\ZG6000\lib\ZGDebugMng.lib D:\ZG\ZG6000\lib\ZGHeartMng.lib D:\ZG\ZG6000\lib\ZGProxyMng.lib D:\ZG\ZG6000\lib\ZGProxyCommon.lib D:\ZG\ZG6000\lib\ZGWebModule.lib E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServer.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSockets.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrent.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPoint.lib shell32.lib  
QMAKE         = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = ..\..\..\tmp\ZGSPWebServer\obj

####### Files

SOURCES       = ..\..\ice\ZGSPWebServer.cpp \
		ZGClientWebModule.cpp \
		ZGDBWebModule.cpp \
		ZGGraphWebModule.cpp \
		ZGRTWebModule.cpp \
		ZGSPWebServerI.cpp \
		ZGSPWebServerMng.cpp \
		ZGUserWebModule.cpp \
		main.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGClientWebModule.cpp \
		..\..\..\tmp\ZGSPWebServer\moc\moc_ZGDBWebModule.cpp \
		..\..\..\tmp\ZGSPWebServer\moc\moc_ZGGraphWebModule.cpp \
		..\..\..\tmp\ZGSPWebServer\moc\moc_ZGRTWebModule.cpp \
		..\..\..\tmp\ZGSPWebServer\moc\moc_ZGSPWebServerMng.cpp \
		..\..\..\tmp\ZGSPWebServer\moc\moc_ZGUserWebModule.cpp
OBJECTS       = ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGClientWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGDBWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGGraphWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGRTWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerI.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerMng.obj \
		..\..\..\tmp\ZGSPWebServer\obj\ZGUserWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\main.obj \
		..\..\..\tmp\ZGSPWebServer\obj\moc_ZGClientWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\moc_ZGDBWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\moc_ZGGraphWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\moc_ZGRTWebModule.obj \
		..\..\..\tmp\ZGSPWebServer\obj\moc_ZGSPWebServerMng.obj \
		..\..\..\tmp\ZGSPWebServer\obj\moc_ZGUserWebModule.obj

DIST          =  ..\..\ice\ZGSPWebServer.h \
		ZGClientWebModule.h \
		ZGDBWebModule.h \
		ZGGraphWebModule.h \
		ZGRTWebModule.h \
		ZGSPWebServerI.h \
		ZGSPWebServerMng.h \
		ZGUserWebModule.h ..\..\ice\ZGSPWebServer.cpp \
		ZGClientWebModule.cpp \
		ZGDBWebModule.cpp \
		ZGGraphWebModule.cpp \
		ZGRTWebModule.cpp \
		ZGSPWebServerI.cpp \
		ZGSPWebServerMng.cpp \
		ZGUserWebModule.cpp \
		main.cpp
QMAKE_TARGET  = ZGSPWebServer
DESTDIR        = ..\..\..\bin\ #avoid trailing-slash linebreak
TARGET         = ZGSPWebServer.exe
DESTDIR_TARGET = ..\..\..\bin\ZGSPWebServer.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{..\..\ice}.cpp{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\ice}.cc{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\ice}.cxx{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\ice}.c{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSPWebServer/uic}.cpp{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSPWebServer/uic}.cc{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSPWebServer/uic}.cxx{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSPWebServer/uic}.c{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSPWebServer\moc}.cpp{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSPWebServer\moc}.cc{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSPWebServer\moc}.cxx{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSPWebServer\moc}.c{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{.}.cpp{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{.}.cc{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{.}.cxx{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

{.}.c{..\..\..\tmp\ZGSPWebServer\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSPWebServer\obj\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Release  ..\..\..\bin\ZGSPWebServer.exe

..\..\..\bin\ZGSPWebServer.exe: E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServer.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSockets.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrent.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPoint.lib $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGClientWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGDBWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGGraphWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGRTWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerI.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerMng.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGUserWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\main.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGClientWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGDBWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGGraphWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGRTWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGSPWebServerMng.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGUserWebModule.obj
$(LIBS)
<<

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ZGSPWebServer.pro -spec win32-msvc "CONFIG+=qml_debug" "CONFIG+=qtquickcompiler" "CONFIG+=force_debug_info" "CONFIG+=separate_debug_info"

qmake_all: FORCE

dist:
	$(ZIP) ZGSPWebServer.zip $(SOURCES) $(DIST) ZGSPWebServer.pro E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_pre.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-desktop.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-vulkan.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-desktop.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\qconfig.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axbase_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bodymovin_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jsonrpc_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_languageserver_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediaquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_functions.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_config.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_post.prf .qmake.stash E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-version.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\toolchain.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_pre.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\default_pre.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resolve_config.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_post.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\build_pass.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\separate_debug_info.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qtquickcompiler.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qml_debug.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\entrypoint.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\precompile_header.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\warn_on.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources_functions.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\moc.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qmake_use.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\file_copies.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\testcase_targets.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exceptions.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\yacc.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\lex.prf ZGSPWebServer.pro E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServer.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSockets.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrent.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPoint.prl    E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy.cpp ..\..\ice\ZGSPWebServer.h ZGClientWebModule.h ZGDBWebModule.h ZGGraphWebModule.h ZGRTWebModule.h ZGSPWebServerI.h ZGSPWebServerMng.h ZGUserWebModule.h  ..\..\ice\ZGSPWebServer.cpp ZGClientWebModule.cpp ZGDBWebModule.cpp ZGGraphWebModule.cpp ZGRTWebModule.cpp ZGSPWebServerI.cpp ZGSPWebServerMng.cpp ZGUserWebModule.cpp main.cpp    

clean: compiler_clean 
	-$(DEL_FILE) ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGClientWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGDBWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGGraphWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGRTWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerI.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerMng.obj ..\..\..\tmp\ZGSPWebServer\obj\ZGUserWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\main.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGClientWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGDBWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGGraphWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGRTWebModule.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGSPWebServerMng.obj ..\..\..\tmp\ZGSPWebServer\obj\moc_ZGUserWebModule.obj
	-$(DEL_FILE) ..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.vc.pdb

distclean: clean 
	-$(DEL_FILE) .qmake.stash ..\..\..\bin\ZGSPWebServer.pdb
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: ..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) ..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h
..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h: E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy.cpp
	cl -BxE:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -Zi -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy.cpp 2>NUL >..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h

compiler_moc_header_make_all: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGClientWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGDBWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGGraphWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGRTWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGSPWebServerMng.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGUserWebModule.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGClientWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGDBWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGGraphWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGRTWebModule.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGSPWebServerMng.cpp ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGUserWebModule.cpp
..\..\..\tmp\ZGSPWebServer\moc\moc_ZGClientWebModule.cpp: ZGClientWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSPWebServer/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSPWebServer -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSPWebServer -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGClientWebModule.h -o ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGClientWebModule.cpp

..\..\..\tmp\ZGSPWebServer\moc\moc_ZGDBWebModule.cpp: ZGDBWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSPWebServer/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSPWebServer -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSPWebServer -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGDBWebModule.h -o ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGDBWebModule.cpp

..\..\..\tmp\ZGSPWebServer\moc\moc_ZGGraphWebModule.cpp: ZGGraphWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSPWebServer/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSPWebServer -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSPWebServer -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGGraphWebModule.h -o ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGGraphWebModule.cpp

..\..\..\tmp\ZGSPWebServer\moc\moc_ZGRTWebModule.cpp: ZGRTWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSPWebServer/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSPWebServer -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSPWebServer -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGRTWebModule.h -o ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGRTWebModule.cpp

..\..\..\tmp\ZGSPWebServer\moc\moc_ZGSPWebServerMng.cpp: ZGSPWebServerMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QTimer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasictimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSPWebServer/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSPWebServer -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSPWebServer -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSPWebServerMng.h -o ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGSPWebServerMng.cpp

..\..\..\tmp\ZGSPWebServer\moc\moc_ZGUserWebModule.cpp: ZGUserWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\..\tmp\ZGSPWebServer\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSPWebServer/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSPWebServer -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSPWebServer -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGUserWebModule.h -o ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGUserWebModule.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServer.obj: ..\..\ice\ZGSPWebServer.cpp ..\..\ice\ZGSPWebServer.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerBase.h \
		..\..\ice\ZGServerCommon.h \
		..\..\include\ZGServerBaseExport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h

..\..\..\tmp\ZGSPWebServer\obj\ZGClientWebModule.obj: ZGClientWebModule.cpp ZGClientWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		ZGSPWebServerMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QTimer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasictimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		..\..\include\ZGProxyCommon.h \
		..\..\include\ZGDebugMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGPubFun.h \
		..\..\include\ZGLogger.h \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h

..\..\..\tmp\ZGSPWebServer\obj\ZGDBWebModule.obj: ZGDBWebModule.cpp ZGDBWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		..\..\include\ZGProxyCommon.h \
		..\..\include\ZGDebugMng.h \
		..\..\include\ZGLogger.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		..\..\include\ZGUtils.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QRegularExpression \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfiledevice.h \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h

..\..\..\tmp\ZGSPWebServer\obj\ZGGraphWebModule.obj: ZGGraphWebModule.cpp ZGGraphWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		..\..\include\ZGProxyCommon.h \
		..\..\include\ZGDebugMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGPubFun.h \
		..\..\include\ZGLogger.h \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGUtils.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QRegularExpression \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfiledevice.h

..\..\..\tmp\ZGSPWebServer\obj\ZGRTWebModule.obj: ZGRTWebModule.cpp ZGRTWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		..\..\include\ZGDebugMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		..\..\include\ZGLogger.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h \
		..\..\include\ZGUtils.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QRegularExpression \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfiledevice.h \
		..\..\include\ZGProxyCommon.h

..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerI.obj: ZGSPWebServerI.cpp ZGSPWebServerI.h \
		..\..\ice\ZGSPWebServer.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerBase.h \
		..\..\ice\ZGServerCommon.h \
		..\..\include\ZGServerBaseExport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		ZGSPWebServerMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QTimer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasictimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h

..\..\..\tmp\ZGSPWebServer\obj\ZGSPWebServerMng.obj: ZGSPWebServerMng.cpp ZGSPWebServerMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QTimer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasictimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\QtConcurrent \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\QtConcurrentDepends \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QtCore \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QtCoreDepends \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20algorithm.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20functional.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20iterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q23functional.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qabstractanimation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qabstracteventdispatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qeventloop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qabstractitemmodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qabstractnativeeventfilter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qabstractproxymodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanimationgroup.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qapplicationstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreapplication.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnativeinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreapplication_platform.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpromise.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassociativeiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbitarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbuffer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraymatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcbormap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborstreamreader.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborstreamwriter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcollator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcommandlineoption.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcommandlineparser.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconcatenatetablesproxymodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdir.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfiledevice.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfileinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdiriterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qeasingcurve.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qendian.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfactoryinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfileselector.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfilesystemwatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuturesynchronizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuturewatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qidentityproxymodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qitemselectionmodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlatin1stringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlibrary.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlibraryinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversionnumber.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qline.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpoint.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlockfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qloggingcategory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmargins.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmessageauthenticationcode.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetaobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmimedata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmimedatabase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmimetype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectcleanuphandler.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoperatingsystemversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qparallelanimationgroup.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpauseanimation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qplugin.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpluginloader.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocess.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qproperty.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpropertyprivate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpropertyanimation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariantanimation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qqueue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrandom.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrect.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsize.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsavefile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedvaluerollback.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsemaphore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsequentialanimationgroup.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsequentialiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsettings.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedmemory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsignalmapper.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsimd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsocketnotifier.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsortfilterproxymodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstack.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstandardpaths.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstorageinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlistmodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemsemaphore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtemporarydir.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtemporaryfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextboundaryfinder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthreadstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtimeline.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtimezone.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtranslator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtransposeproxymodel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarianthash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariantlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariantmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvector.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qwaitcondition.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDeadlineTimer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qwineventnotifier.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxmlstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxpfunctional.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtaskbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentstoredfunctioncall.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrent_global.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentrunbase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentcompilertest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentfilter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentfilterkernel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentiteratekernel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentmedian.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentthreadengine.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentmapkernel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentreducekernel.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentfunctionwrappers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentrun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrenttask.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent\qtconcurrentversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServer \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qabstracthttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QAbstractSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkProxy \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkproxy.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslError \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsockets_global.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qtwebsocketsexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocketprotocol.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterrule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponse.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslCertificate \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslKey \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslkey.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerResponse \
		ZGClientWebModule.h \
		ZGDBWebModule.h \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		..\..\include\ZGDebugMng.h \
		..\..\include\ZGLogger.h \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		..\..\include\ZGProxyCommon.h \
		ZGRTWebModule.h \
		..\..\include\ZGRuntime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QThread \
		ZGUserWebModule.h \
		..\..\include\ZGUtils.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QRegularExpression \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFile \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h \
		..\..\include\ZGSecure.h

..\..\..\tmp\ZGSPWebServer\obj\ZGUserWebModule.obj: ZGUserWebModule.cpp ZGUserWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		..\..\include\ZGProxyCommon.h \
		..\..\include\ZGDebugMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGPubFun.h \
		..\..\include\ZGLogger.h \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h

..\..\..\tmp\ZGSPWebServer\obj\main.obj: main.cpp ..\..\include\ZGServerApplication.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QCoreApplication \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreapplication.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qeventloop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnativeinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreapplication_platform.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpromise.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QUdpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qudpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QHostAddress \
		..\..\include\ZGRuntime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QThread \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		..\..\ice\ZGServerCommon.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		..\..\include\ZGMqttClient.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		..\..\include\ZGProxyMng.h \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGHeartMng.h \
		..\..\include\ZGDebugMng.h \
		..\..\include\ZGLogger.h \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		ZGSPWebServerI.h

..\..\..\tmp\ZGSPWebServer\obj\moc_ZGClientWebModule.obj: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGClientWebModule.cpp 

..\..\..\tmp\ZGSPWebServer\obj\moc_ZGDBWebModule.obj: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGDBWebModule.cpp 

..\..\..\tmp\ZGSPWebServer\obj\moc_ZGGraphWebModule.obj: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGGraphWebModule.cpp 

..\..\..\tmp\ZGSPWebServer\obj\moc_ZGRTWebModule.obj: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGRTWebModule.cpp 

..\..\..\tmp\ZGSPWebServer\obj\moc_ZGSPWebServerMng.obj: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGSPWebServerMng.cpp 

..\..\..\tmp\ZGSPWebServer\obj\moc_ZGUserWebModule.obj: ..\..\..\tmp\ZGSPWebServer\moc\moc_ZGUserWebModule.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

