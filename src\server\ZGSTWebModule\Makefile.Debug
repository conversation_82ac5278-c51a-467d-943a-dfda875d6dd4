#############################################################################
# Makefile for building: ZGSTWebModuled
# Generated by qmake (3.1) (Qt 6.5.3)
# Project:  ZGSTWebModule.pro
# Template: lib
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_PLUGIN -DQT_GUI_LIB -DQT_HTTPSERVER_LIB -DQT_WEBSOCKETS_LIB -DQT_NETWORK_LIB -DQT_CONCURRENT_LIB -DQT_CORE_LIB -D_WINDLL
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -Zi -MDd -utf-8 -W3 -w44456 -w44457 -w44458 /Fd..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModuled.vc.pdb $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr /bigobj -Zi -MDd -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc /Fd..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModuled.vc.pdb $(DEFINES)
INCPATH       = -I. -IE:\Library\ice-3.7.6\include -I. -I..\..\include -I..\..\include\thirdparty -I..\..\ice -IE:\Qt\6.5.3\msvc2019_64\include -IE:\Qt\6.5.3\msvc2019_64\include\QtGui -IE:\Qt\6.5.3\msvc2019_64\include\QtHttpServer -IE:\Qt\6.5.3\msvc2019_64\include\QtWebSockets -IE:\Qt\6.5.3\msvc2019_64\include\QtNetwork -IE:\Qt\6.5.3\msvc2019_64\include\QtConcurrent -IE:\Qt\6.5.3\msvc2019_64\include\QtCore -I..\..\..\tmp\ZGSTWebModuled\moc -I/include -IE:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /DEBUG /DLL /SUBSYSTEM:CONSOLE
LIBS          = /LIBPATH:D:\ZG\ZG6000\lib /LIBPATH:D:\ZG\ZG6000\bin /LIBPATH:E:\Library\ice-3.7.6\lib\x64\Release /LIBPATH:E:\Library\ice-3.7.6\lib\x64\Debug ZGPubFund.lib ZGRuntimed.lib ZGDebugMngd.lib ZGProxyMngd.lib ZGProxyCommond.lib ZGWebModuled.lib ZGJsond.lib E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Guid.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServerd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSocketsd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Networkd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrentd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored.lib   
QMAKE         = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = ..\..\..\tmp\ZGSTWebModuled\obj

####### Files

SOURCES       = ..\..\ice\ZGSTStraySystem.cpp \
		..\..\ice\ZGServerBase.cpp \
		ZGSTHandle.cpp \
		ZGSTWebModule.cpp ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTHandle.cpp \
		..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTWebModule.cpp
OBJECTS       = ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTStraySystem.obj \
		..\..\..\tmp\ZGSTWebModuled\obj\ZGServerBase.obj \
		..\..\..\tmp\ZGSTWebModuled\obj\ZGSTHandle.obj \
		..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModule.obj \
		..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTHandle.obj \
		..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTWebModule.obj

DIST          = ZGSTWebModule.json ..\..\ice\ZGSTStraySystem.h \
		..\..\ice\ZGServerBase.h \
		ZGSTHandle.h \
		ZGSTWebModule.h ..\..\ice\ZGSTStraySystem.cpp \
		..\..\ice\ZGServerBase.cpp \
		ZGSTHandle.cpp \
		ZGSTWebModule.cpp
QMAKE_TARGET  = ZGSTWebModuled
DESTDIR        = ..\..\..\lib\ #avoid trailing-slash linebreak
TARGET         = ZGSTWebModuled.dll
DESTDIR_TARGET = ..\..\..\lib\ZGSTWebModuled.dll

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{..\..\..\tmp\ZGSTWebModuled\moc}.cpp{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSTWebModuled\moc}.cc{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSTWebModuled\moc}.cxx{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\..\tmp\ZGSTWebModuled\moc}.c{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\ice}.cpp{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\ice}.cc{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\ice}.cxx{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{..\..\ice}.c{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSTWebModuled/uic}.cpp{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSTWebModuled/uic}.cc{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSTWebModuled/uic}.cxx{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{D:\ZG\ZG6000/tmp//ZGSTWebModuled/uic}.c{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{.}.cpp{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{.}.cc{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{.}.cxx{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

{.}.c{..\..\..\tmp\ZGSTWebModuled\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\..\..\tmp\ZGSTWebModuled\obj\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Debug  ..\..\..\lib\ZGSTWebModuled.dll

..\..\..\lib\ZGSTWebModuled.dll: E:\Qt\6.5.3\msvc2019_64\lib\Qt6Guid.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServerd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSocketsd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Networkd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrentd.lib E:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored.lib $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
..\..\..\tmp\ZGSTWebModuled\obj\ZGSTStraySystem.obj ..\..\..\tmp\ZGSTWebModuled\obj\ZGServerBase.obj ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTHandle.obj ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModule.obj ..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTHandle.obj ..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTWebModule.obj
$(LIBS)
<<
	-$(COPY_FILE) $(DESTDIR_TARGET) ..\..\..\bin

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ZGSTWebModule.pro -spec win32-msvc "CONFIG+=qtquickcompiler"

qmake_all: FORCE

dist:
	$(ZIP) ZGSTWebModuled.zip $(SOURCES) $(DIST) ZGSTWebModule.pro E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_pre.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-desktop.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-vulkan.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-desktop.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\qconfig.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axbase_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bodymovin_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jsonrpc_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_languageserver_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediaquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_functions.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_config.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_post.prf .qmake.stash E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-version.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\toolchain.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_pre.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\default_pre.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resolve_config.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_post.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\build_pass.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\console.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qtquickcompiler.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\entrypoint.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\precompile_header.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\warn_on.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources_functions.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\moc.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\opengl.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qmake_use.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\file_copies.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\testcase_targets.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exceptions.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\yacc.prf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\lex.prf ZGSTWebModule.pro E:\Qt\6.5.3\msvc2019_64\lib\Qt6Guid.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServerd.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSocketsd.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6Networkd.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrentd.prl E:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored.prl    E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy.cpp ..\..\ice\ZGSTStraySystem.h ..\..\ice\ZGServerBase.h ZGSTHandle.h ZGSTWebModule.h  ..\..\ice\ZGSTStraySystem.cpp ..\..\ice\ZGServerBase.cpp ZGSTHandle.cpp ZGSTWebModule.cpp    

clean: compiler_clean 
	-$(DEL_FILE) ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTStraySystem.obj ..\..\..\tmp\ZGSTWebModuled\obj\ZGServerBase.obj ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTHandle.obj ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModule.obj ..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTHandle.obj ..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTWebModule.obj
	-$(DEL_FILE) ..\..\..\lib\ZGSTWebModuled.exp ..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModuled.vc.pdb ..\..\..\lib\ZGSTWebModuled.ilk ..\..\..\lib\ZGSTWebModuled.idb

distclean: clean 
	-$(DEL_FILE) .qmake.stash ..\..\..\lib\ZGSTWebModuled.lib ..\..\..\lib\ZGSTWebModuled.pdb
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: ..\..\..\tmp\ZGSTWebModuled\moc\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) ..\..\..\tmp\ZGSTWebModuled\moc\moc_predefs.h
..\..\..\tmp\ZGSTWebModuled\moc\moc_predefs.h: E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy.cpp
	cl -BxE:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr /bigobj -Zi -MDd -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy.cpp 2>NUL >..\..\..\tmp\ZGSTWebModuled\moc\moc_predefs.h

compiler_moc_header_make_all: ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTHandle.cpp ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTWebModule.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTHandle.cpp ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTWebModule.cpp
..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTHandle.cpp: ZGSTHandle.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServer \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qabstracthttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QAbstractSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkProxy \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkproxy.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslError \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsockets_global.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qtwebsocketsexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocketprotocol.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterrule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponse.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpromise.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\ice\ZGSTStraySystem.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\..\tmp\ZGSTWebModuled\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTWebModuled/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSTWebModule -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTWebModule -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtGui -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTHandle.h -o ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTHandle.cpp

..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTWebModule.cpp: ZGSTWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\..\tmp\ZGSTWebModuled\moc\moc_predefs.h \
		E:\Qt\6.5.3\msvc2019_64\bin\moc.exe
	E:\Qt\6.5.3\msvc2019_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTWebModuled/moc/moc_predefs.h -IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc -ID:/ZG/ZG6000/src/server/ZGSTWebModule -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTWebModule -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -IE:/Qt/6.5.3/msvc2019_64/include -IE:/Qt/6.5.3/msvc2019_64/include/QtGui -IE:/Qt/6.5.3/msvc2019_64/include/QtHttpServer -IE:/Qt/6.5.3/msvc2019_64/include/QtWebSockets -IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork -IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent -IE:/Qt/6.5.3/msvc2019_64/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTWebModule.h -o ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTWebModule.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

..\..\..\tmp\ZGSTWebModuled\obj\ZGSTStraySystem.obj: ..\..\ice\ZGSTStraySystem.cpp ..\..\ice\ZGSTStraySystem.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerBase.h \
		..\..\ice\ZGServerCommon.h \
		..\..\include\ZGServerBaseExport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h

..\..\..\tmp\ZGSTWebModuled\obj\ZGServerBase.obj: ..\..\ice\ZGServerBase.cpp ..\..\ice\ZGServerBase.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerCommon.h \
		..\..\include\ZGServerBaseExport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h

..\..\..\tmp\ZGSTWebModuled\obj\ZGSTHandle.obj: ZGSTHandle.cpp ZGSTHandle.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServer \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qabstracthttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QAbstractSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkProxy \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkproxy.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslError \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsockets_global.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qtwebsocketsexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocketprotocol.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterrule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponse.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpromise.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		..\..\ice\ZGSTStraySystem.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h \
		..\..\include\ZGProxyCommon.h \
		..\..\include\ZGDebugMng.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutex \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMutexLocker \
		..\..\include\ZGPubFun.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMap \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDateTime \
		..\..\include\ZGLogger.h \
		..\..\include\thirdparty\log4cplus\tstring.h \
		..\..\include\thirdparty\log4cplus\config.hxx \
		..\..\include\thirdparty\log4cplus\config\win32.h \
		..\..\include\thirdparty\log4cplus\config\macosx.h \
		..\..\include\thirdparty\log4cplus\config\defines.hxx \
		..\..\include\thirdparty\log4cplus\helpers\thread-config.h \
		..\..\include\thirdparty\log4cplus\tchar.h \
		..\..\include\redis\ZGRedisClient.h \
		..\..\include\redis\redis.h \
		..\..\include\thirdparty\hiredis\hiredis.h \
		..\..\include\thirdparty\hiredis\read.h \
		..\..\include\thirdparty\hiredis\sds.h \
		..\..\include\thirdparty\hiredis\alloc.h \
		..\..\include\redis\utils.h \
		..\..\include\redis\redisexcept.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QDebug \
		..\..\include\ZGMqttClient.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QReadWriteLock \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qreadwritelock.h \
		..\..\include\thirdparty\QtMqtt\qmqttclient.h \
		..\..\include\thirdparty\QtMqtt\qmqttglobal.h \
		..\..\include\thirdparty\QtMqtt\qmqttauthenticationproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqtttype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QList \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QPair \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QHash \
		..\..\include\thirdparty\QtMqtt\qmqttconnectionproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttpublishproperties.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscription.h \
		..\..\include\thirdparty\QtMqtt\qmqttmessage.h \
		..\..\include\thirdparty\QtMqtt\qmqtttopicname.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QExplicitlySharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QMetaType \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QStringList \
		..\..\include\thirdparty\QtMqtt\qmqtttopicfilter.h \
		..\..\include\thirdparty\QtMqtt\qmqttsubscriptionproperties.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QIODevice \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QTcpSocket \
		..\..\include\ZGProxyMng.h \
		E:\Library\ice-3.7.6\include\Ice\Ice.h \
		E:\Library\ice-3.7.6\include\Ice\Initialize.h \
		E:\Library\ice-3.7.6\include\Ice\Communicator.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsync.h \
		E:\Library\ice-3.7.6\include\Ice\InstrumentationF.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectFactory.h \
		E:\Library\ice-3.7.6\include\Ice\Router.h \
		E:\Library\ice-3.7.6\include\Ice\Locator.h \
		E:\Library\ice-3.7.6\include\Ice\Process.h \
		E:\Library\ice-3.7.6\include\Ice\PluginF.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContextF.h \
		E:\Library\ice-3.7.6\include\Ice\Properties.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\FacetMap.h \
		E:\Library\ice-3.7.6\include\Ice\Connection.h \
		E:\Library\ice-3.7.6\include\Ice\Endpoint.h \
		E:\Library\ice-3.7.6\include\Ice\PropertiesF.h \
		E:\Library\ice-3.7.6\include\Ice\Dispatcher.h \
		E:\Library\ice-3.7.6\include\Ice\Plugin.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\Logger.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerUtil.h \
		E:\Library\ice-3.7.6\include\Ice\RemoteLogger.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorAsync.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapter.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocator.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedData.h \
		E:\Library\ice-3.7.6\include\Ice\Application.h \
		E:\Library\ice-3.7.6\include\IceUtil\CtrlCHandler.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionAsync.h \
		E:\Library\ice-3.7.6\include\Ice\Functional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Functional.h \
		E:\Library\ice-3.7.6\include\Ice\ImplicitContext.h \
		E:\Library\ice-3.7.6\include\Ice\DispatchInterceptor.h \
		E:\Library\ice-3.7.6\include\Ice\NativePropertiesAdmin.h \
		E:\Library\ice-3.7.6\include\Ice\Metrics.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksums.h \
		E:\Library\ice-3.7.6\include\Ice\SliceChecksumDict.h \
		E:\Library\ice-3.7.6\include\Ice\Service.h \
		E:\Library\ice-3.7.6\include\Ice\RegisterPlugins.h \
		E:\Library\ice-3.7.6\include\Ice\InterfaceByValue.h \
		E:\Library\ice-3.7.6\include\Ice\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\ConsoleUtil.h \
		E:\Library\ice-3.7.6\include\Ice\IconvStringConverter.h \
		E:\Library\ice-3.7.6\include\IceUtil\StringUtil.h \
		E:\Library\ice-3.7.6\include\Ice\UUID.h \
		E:\Library\ice-3.7.6\include\IceUtil\UUID.h \
		..\..\include\ZGProxyServer.h \
		..\..\ice\ZGSPSystemNode.h \
		..\..\ice\ZGSPSystemInfo.h \
		..\..\ice\ZGSPSystemUtils.h \
		..\..\ice\ZGSPSystemServer.h \
		..\..\ice\ZGSPSystemService.h \
		..\..\ice\ZGSPSystemServiceInstance.h \
		..\..\ice\ZGSPDBData.h \
		..\..\ice\ZGSPMySQLMaster.h \
		..\..\ice\ZGSPRTData.h \
		..\..\ice\ZGSPModifyOnline.h \
		..\..\ice\ZGSPScriptProcess.h \
		..\..\ice\ZGSPTimeSyn.h \
		..\..\ice\ZGSPClientManager.h \
		..\..\ice\ZGSPDataDispatch.h \
		..\..\ice\ZGSPDataPublish.h \
		..\..\ice\ZGSPEventProcess.h \
		..\..\ice\ZGSPEventParse.h \
		..\..\ice\ZGSPGPIOServer.h \
		..\..\ice\ZGSPHistoryMaintain.h \
		..\..\ice\ZGSPEventTrigger.h \
		..\..\ice\ZGSPVoicePlay.h \
		..\..\ice\ZGSPRedisMaster.h \
		..\..\ice\ZGSPStoreChange.h \
		..\..\ice\ZGSPStatisticStore.h \
		..\..\ice\ZGStatisticBase.h \
		..\..\ice\ZGSPStatisticDispatch.h \
		..\..\ice\ZGSPRTStatisticProcess.h \
		..\..\ice\ZGSPUserManager.h \
		..\..\ice\ZGSPPowerVerify.h \
		..\..\ice\ZGSPHisStatisticProcess.h \
		..\..\ice\ZGSPHisDataManager.h \
		..\..\ice\ZGSPAppNodeManager.h \
		..\..\ice\ZGSPExamManager.h \
		..\..\ice\ZGSPGraphicTopology.h \
		..\..\ice\ZGSPAIEngine.h \
		..\..\ice\ZGSPAIDispatch.h \
		..\..\ice\ZGSPAliSMS.h \
		..\..\ice\ZGSPMessageGateway.h \
		..\..\ice\ZGSPWebServer.h \
		..\..\ice\ZGMPBroadcastServer.h \
		..\..\ice\ZGMPPortSend.h \
		..\..\ice\ZGMPRuleEngine.h \
		..\..\ice\ZGMPCommandProcess.h \
		..\..\ice\ZGMPDeviceManager.h \
		..\..\ice\ZGMPDatasetPublish.h \
		..\..\ice\ZGMPDatasetProperty.h \
		..\..\ice\ZGMPRegionManager.h \
		..\..\ice\ZGMPIdentifyManager.h \
		..\..\ice\ZGMPPortRecv.h \
		..\..\ice\ZGMPStoreChange.h \
		..\..\ice\ZGMPEventParse.h \
		..\..\ice\ZGMPDeviceProperty.h \
		..\..\ice\ZGMPTaskManager.h \
		..\..\ice\ZGMPHisStatisticProcess.h \
		..\..\ice\ZGMPRuntimeProcess.h \
		..\..\ice\ZGMPVideoStream.h \
		..\..\ice\ZGMPVideoTranscode.h \
		..\..\ice\ZGMPVideoHIK.h \
		..\..\ice\ZGMPLocalProcess.h \
		..\..\ice\ZGMPRealWarn.h \
		..\..\ice\ZGOPPatrolDeviceCtrl.h \
		..\..\ice\ZGOPTaskBase.h \
		..\..\ice\ZGOPTaskManager.h \
		..\..\ice\ZGOPTaskOT.h \
		..\..\ice\ZGOPTaskIT.h \
		..\..\ice\ZGOPTaskIU.h \
		..\..\ice\ZGOPWPManager.h \
		..\..\ice\ZGOPTaskOutage.h \
		..\..\ice\ZGPTLIec104Client.h \
		..\..\ice\ZGPTLServer.h \
		..\..\ice\ZGPTLIec104Server.h \
		..\..\ice\ZGPTLInspectionRobot.h \
		..\..\ice\ZGPTLModbusTCPClient.h \
		..\..\ice\ZGPTLModbusTCPServer.h \
		..\..\ice\ZGPTLModbusRTUClient.h \
		..\..\ice\ZGPTLNetLed.h \
		..\..\ice\ZGSTStrayDevice.h \
		..\..\ice\ZGGRGroundReflux.h \
		..\..\ice\ZGDPDeviceManager.h \
		..\..\ice\ZGDPDeviceProperty.h \
		..\..\ice\ZGSIMServer.h \
		..\..\ice\ZGSIMModbusTCPServer.h \
		..\..\include\ZGJson.h \
		..\..\include\ptl\PTLDefine.h \
		..\..\include\ZGUtils.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QRegularExpression \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFile \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfile.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfiledevice.h

..\..\..\tmp\ZGSTWebModuled\obj\ZGSTWebModule.obj: ZGSTWebModule.cpp ZGSTWebModule.h \
		..\..\include\ZGWebModule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversionchecks.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconfig.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcore-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtconfigmacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtcoreexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtpreprocessorsupport.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtnoop.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsystemdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qprocessordetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompilerdetection.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qassert.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypes.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtclasshelpermacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtversion.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtypeinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsysinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlogging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qflags.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbasicatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qatomic_cxx11.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qgenericatomic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qconstructormacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdarwinhelpers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexceptionhandling.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qforeach.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtdeprecationmarkers.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttypetraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfunctionpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qglobalstatic.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmalloc.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qminmax.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnumeric.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qoverload.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qswap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtenvironmentvariables.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtresource.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qttranslation.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qversiontagging.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonDocument \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsondocument.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstring.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qchar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrefcount.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qnamespace.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtmetamacros.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpair.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydatapointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qarraydataops.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainertools_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qxptype_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearrayview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringfwd.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20type_traits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringliteral.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qanystringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qutf8stringview.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringtokenizer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringbuilder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhashfunctions.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborvalue.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatetime.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcalendar.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlocale.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvariant.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetatype.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcompare.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdatastream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevicebase.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfloat16.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmath.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qalgorithms.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmetacontainer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontainerinfo.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtaggedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobjectdefs_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qscopeguard.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdebug.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtextstream.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringconverter_base.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcontiguouscache.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qsharedpointer_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiterator.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbytearraylist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringlist.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qstringmatcher.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmap.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qshareddata_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qset.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qhash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qvarlengtharray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\q20memory.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcoreevent.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qobject_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qbindingstorage.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcborcommon.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qregularexpression.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\quuid.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonobject.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QJsonArray \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qjsonarray.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServerRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qthttpserverexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qurlquery.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qhostaddress.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkglobal.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetwork-config.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtnetworkexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qabstractsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qiodevice.h \
		..\..\ice\ZGServerCommon.h \
		E:\Library\ice-3.7.6\include\IceUtil\PushDisableWarnings.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyF.h \
		E:\Library\ice-3.7.6\include\Ice\Config.h \
		E:\Library\ice-3.7.6\include\IceUtil\Config.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyHandle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Handle.h \
		E:\Library\ice-3.7.6\include\IceUtil\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectF.h \
		E:\Library\ice-3.7.6\include\IceUtil\Shared.h \
		E:\Library\ice-3.7.6\include\IceUtil\Atomic.h \
		E:\Library\ice-3.7.6\include\IceUtil\Mutex.h \
		E:\Library\ice-3.7.6\include\IceUtil\Lock.h \
		E:\Library\ice-3.7.6\include\IceUtil\ThreadException.h \
		E:\Library\ice-3.7.6\include\IceUtil\Time.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexProtocol.h \
		E:\Library\ice-3.7.6\include\Ice\Handle.h \
		E:\Library\ice-3.7.6\include\Ice\ValueF.h \
		E:\Library\ice-3.7.6\include\Ice\Exception.h \
		E:\Library\ice-3.7.6\include\Ice\Format.h \
		E:\Library\ice-3.7.6\include\Ice\SlicedDataF.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObject.h \
		E:\Library\ice-3.7.6\include\Ice\LocalObjectF.h \
		E:\Library\ice-3.7.6\include\Ice\StreamHelpers.h \
		E:\Library\ice-3.7.6\include\IceUtil\ScopedArray.h \
		E:\Library\ice-3.7.6\include\IceUtil\Iterator.h \
		E:\Library\ice-3.7.6\include\Ice\Comparable.h \
		E:\Library\ice-3.7.6\include\Ice\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\Optional.h \
		E:\Library\ice-3.7.6\include\IceUtil\UndefSysMacros.h \
		E:\Library\ice-3.7.6\include\IceUtil\PopDisableWarnings.h \
		ZGSTHandle.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QObject \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\QHttpServer \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qabstracthttpserver.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qssl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QFlags \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QUrl \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QAbstractSocket \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkRequest \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkrequest.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QSharedDataPointer \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QString \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\QVariant \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QNetworkProxy \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qnetworkproxy.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslError \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslerror.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslcertificate.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qcryptographichash.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\QSslConfiguration \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslconfiguration.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qsslsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtNetwork\qtcpsocket.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsockets_global.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qtwebsocketsexports.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtWebSockets\qwebsocketprotocol.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouter.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverrouterrule.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponse.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverresponder.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtHttpServer\qhttpserverviewtraits.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfutureinterface.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qmutex.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qtsan_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qresultstore.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qfuture_impl.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthreadpool.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qthread.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qdeadlinetimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qelapsedtimer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qrunnable.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qexception.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpointer.h \
		E:\Qt\6.5.3\msvc2019_64\include\QtCore\qpromise.h \
		..\..\ice\ZGSTStraySystem.h \
		E:\Library\ice-3.7.6\include\Ice\Proxy.h \
		E:\Library\ice-3.7.6\include\Ice\ProxyFactoryF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionIF.h \
		E:\Library\ice-3.7.6\include\Ice\RequestHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointF.h \
		E:\Library\ice-3.7.6\include\Ice\EndpointTypes.h \
		E:\Library\ice-3.7.6\include\Ice\Object.h \
		E:\Library\ice-3.7.6\include\Ice\IncomingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\Current.h \
		E:\Library\ice-3.7.6\include\Ice\ObjectAdapterF.h \
		E:\Library\ice-3.7.6\include\Ice\ConnectionF.h \
		E:\Library\ice-3.7.6\include\Ice\Identity.h \
		E:\Library\ice-3.7.6\include\Ice\Version.h \
		E:\Library\ice-3.7.6\include\Ice\ReferenceF.h \
		E:\Library\ice-3.7.6\include\Ice\BatchRequestQueueF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResult.h \
		E:\Library\ice-3.7.6\include\IceUtil\Monitor.h \
		E:\Library\ice-3.7.6\include\IceUtil\Cond.h \
		E:\Library\ice-3.7.6\include\Ice\CommunicatorF.h \
		E:\Library\ice-3.7.6\include\Ice\AsyncResultF.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsync.h \
		E:\Library\ice-3.7.6\include\IceUtil\Timer.h \
		E:\Library\ice-3.7.6\include\IceUtil\Thread.h \
		E:\Library\ice-3.7.6\include\Ice\OutgoingAsyncF.h \
		E:\Library\ice-3.7.6\include\Ice\OutputStream.h \
		E:\Library\ice-3.7.6\include\Ice\InstanceF.h \
		E:\Library\ice-3.7.6\include\Ice\Buffer.h \
		E:\Library\ice-3.7.6\include\Ice\Protocol.h \
		E:\Library\ice-3.7.6\include\Ice\InputStream.h \
		E:\Library\ice-3.7.6\include\Ice\LoggerF.h \
		E:\Library\ice-3.7.6\include\Ice\ValueFactory.h \
		E:\Library\ice-3.7.6\include\Ice\UserExceptionFactory.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTable.h \
		E:\Library\ice-3.7.6\include\Ice\ObserverHelper.h \
		E:\Library\ice-3.7.6\include\Ice\Instrumentation.h \
		E:\Library\ice-3.7.6\include\Ice\LocalException.h \
		E:\Library\ice-3.7.6\include\Ice\ExceptionHelpers.h \
		E:\Library\ice-3.7.6\include\Ice\BuiltinSequences.h \
		E:\Library\ice-3.7.6\include\Ice\UniquePtr.h \
		E:\Library\ice-3.7.6\include\Ice\GCObject.h \
		E:\Library\ice-3.7.6\include\IceUtil\MutexPtrLock.h \
		E:\Library\ice-3.7.6\include\Ice\Value.h \
		E:\Library\ice-3.7.6\include\Ice\Incoming.h \
		E:\Library\ice-3.7.6\include\Ice\ServantLocatorF.h \
		E:\Library\ice-3.7.6\include\Ice\ServantManagerF.h \
		E:\Library\ice-3.7.6\include\Ice\ResponseHandlerF.h \
		E:\Library\ice-3.7.6\include\Ice\FactoryTableInit.h \
		E:\Library\ice-3.7.6\include\Ice\DefaultValueFactory.h \
		..\..\ice\ZGServerBase.h \
		..\..\include\ZGServerBaseExport.h

..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTHandle.obj: ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTHandle.cpp 

..\..\..\tmp\ZGSTWebModuled\obj\moc_ZGSTWebModule.obj: ..\..\..\tmp\ZGSTWebModuled\moc\moc_ZGSTWebModule.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

