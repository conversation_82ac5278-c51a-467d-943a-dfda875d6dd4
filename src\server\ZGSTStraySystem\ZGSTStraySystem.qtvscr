<FlowDocument xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" FontFamily="Segoe UI" FontSize="12">
  <FlowDocument.Tag><![CDATA[{
  "timestamp": "2025-04-30 16:01:21",
  "files": {
    "D:\\ZG\\ZG6000\\src\\server\\ZGSTStraySystem\\ZGSTStraySystem.vcxproj": {
      "before": "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",
      "after": "7Vt7d9rIFd+/e06/g8pJFnu3CDDYjg1oDxbYSwvYMWTT5nDKEdIYtNGDlUa2abffvXeekkACY+8mTZvdJEgzd5739bt3Rv9ofvNN84dH11HuURDavtcqVNVKQUGe6Vu2N28VInxXelP4QfvjH5o3gf8zMrHSQXdG5OCxEcwRDluFi8h2rIICvXjwtsB4eV4uh+YCuUaourYZ+KF/h1XTd8sWukeOv0RB2Q1npFn5qFKpFaB3RWn2MHKvAj9aKn1jhpxWgQ+o+96dPY8CA8MEQ0YM5Fm1Ss8znchCrcItcpARol8fT+qiBbRJdaVxmmY5XUwmQ/5r3jgGvvMDV4NOmmX5xuqhgO1HVuMdk+ugWTTfOjVK8ZtPrFmWe8y2HJYAzMCr1LZfOf7McDY2+iqyLe1fx3pVPz2rHJf0k9pRqXait0tntapeOj3TL99U9bp+2r34t9wb2obv163v46HhonBpmEj7cDUaj3BgrEarEPjeLKereZu/otWDH1jaW/zTaHpfqwAbRBGneG97lv8QMmEULPqJCbNWragVtXpWqYNQN8tbSbd1N7C9PXpMUPNO3+JBSJVEAVmxbCLGrULx1YEsPyy2WsWicn2r/Kn7aIc4PEjVTn7BKmbaVjwsaK8OBiPaHRfAjh2AWvrB6nAie2yW5SPjNJHWmNVc4dylH2CFd9MqvDr4SedafWPgxeFkIJVXXy5VrvfqEnoKC0o5S4TSC0ypxuGvrw4Eg+iCEypaLAidTzURmis1cez7Toiwdl+tH8UaKUr5dl9HeBlhuSuaqk7Yn5ntTZrl9Wreqj3uA+ds1/4nCvXbyBvbLurbs8AIVu9CY460O9AJ1CzvpOP96QsjMEyMghHMd+hj+AF9ThYKwqR5G6+WSGsvl45tUnO3ZgJoNW/X86BzF1m2gVHGYrG7nKwp2cSf/QwbkN2Q93oT2C4smW3Sppamq0kbquvrkpUStjWpT3F4QyikafwqEtzj/a4iYb1YJiwiAEmZyRWK3n7mJm1mNhtLA3cI9nFqMVASCuuUMEQ5JnWtCVjWbJPGwchbosEYMBGYvkTnYKi3C3TKylGTCTrzFve8EBuOo52ox2pt6ob35lGlejYlQCOu5DoJK/WtyEGhZvoBangIg1P82DB9z4yCAHlgWGKS3O1P6+QLV5VQ08+5JuatFAIrWgUpEGBtL/3IA0x6gQC/Ie7TWgU9CrHvUt/Z0B3dd5e2g1Ls3O1/8730W8yEj/hozrkBConvUJj0Gp4J0/zRni8Kyhg94sSUzxXTjxxL8XysOD7YfqTETv/P5JmqQ0OBHwrCXWNFiSmKhsYgByZ2VqoU4jJbNXPSbPwUyus+YuQRyB+LNffoSeJnS7rw50LqRgsEwYLcmE11fheigGBAcGXrwCOBG9QIyDJ0HEnYtHc/seKDYm7OS0oVQVdiZL5T5cRWbW70PnuX0Kf/050TgpISUsLMgUEiSCnXKTqhZwBXQG5eYJc3MSL0J3oHwPQb9b4VlCVGZLpLzNoL1rSG/prcIpBe5crmHthHJsUc7L5gQBxEAJB7m32K4fq29xEi9AC54LQM5wVDcTC+1qEY5xnikFDAjIAhwZpnCENm3xmikABjiQGfKQvJQdckASBb3KnYswy27cWf5HjbBSEzYiCJCQgwIQIj4TEzAvsY0EyYJX08XyQY+LbFAnDD4aki2GkaPdsAr9RG93zC9WBim6hUU0/Vk4nNkkoNZiQy3yZ4YQfW0oBciiAzEX+CZhnMnri+2Xh9sG0+hxBtbpsuAxmpRV0vaZJMK30wz4N7w4mQboRYIa+259geoo8hDmwTQ/qFwFlagheB/wBvQ/SglCCac+0wtO9RiVZOp+bSiULyl74DdEGBB9yBzM3jMlBKNEmolB5q9Wqlwn7fnMFvvX52dkJ/68fi95S/v1GSi+fzTq1YrCVeZhgid+agoA85EphsnwAlkJZYYbP2mQW97by2svcL2IEQ9TyS8WPdciOzWSHbUKFPNLmkbSHc98AQ5lTGje3QgNWMlsi072zzvRF4hB9a/fj0tFGvn5yCeHSyaWCfcmpiSe8+mohKw4+GZwHr59po5ZnN8ma5nFHf8OYR4NURhiYG5N1CbJnLZfW0Wd6oko2uZwSQXgKSpp5FetJ8Xqy1iHuC6UL+hW3+wHiEnUFgq4gcyGJJfBMgwMImAGw/iA1HqE316+Hout9tvBv29OtOtzEVD+97w9pRY9odti/63Wn3b+PusNPtTNv93tUQfkfj69v2VbcBZCf1Rk/vTvWbm2p1Omjf3PSGVw0gvnh31Xg7ng6vp+z59UHOLIA/OTUxf2KCsU92TySYNsoTS/bngeF2DGxcQG5bbjk1qJk1sikktGATRUJLG0D+3h4vAmRYyOr0+5B+TRPIhqNouQxgk0EkAhwtLwzPQ4HGDHx2nWw6hu7xe5LymuI2TYBC1EuzGax5fr3sAvDfZeQ4JBsZiv1JlUlKrj19csCg0X9rkPFlKsUKJSldPMBIJjssCmQyx+aVX8+6gJyciB2F8yQ4JOZsbLQ7aIk8C05TiHfpnENCDv6cVCqViWPP4JHvugpvjYzqm2h2GXl5tSMUwKkNkYQ8ilvIDoa6YwPayiP5S+jvGCCRjczrhFq6gTfPq/8RgfBsqQdmPK521QOj3Py5Dn7BOLHSTD9O9hyOciYcKxAPXzv9/vtqlc476Y6SfEv5pGRFFsM5eEiCinW+ZmOMtbltJ6L7nQIPm+Ompr1ZnTV54XAL5UF72Lvsjsad7g0xkkP97+cYMtOtImCE2lFR8UjCpRgfD/CjFZXxqATQAAeQqS/KY8XiCZzEwP9FZRnNILkNpzhj/yPyWsWT47Pj2Um9Wq+b5l3VuisqDvdCreJ3QC70tB2YCxuDp4kCGPo7SA8nGfYk/EAMZ/cRmREBDWBm70EvCHxgap9XK+1G1yPuWr8edNrjS98h56O8aVaNbHaFwGKSHD059EtgBWHPcutlDxnQnA+cVSObrQdHW2MmQJHcz6JbdIcgqwjmkY+SUSEHYQcF1H+9OmBx1+H6uYOKHgEOJShl61vAGT45crGIGQO7SVwNHzW7TjYdRTN2dKgRIOo7MEJclKB6nvsCwBOb9ObAtpxYYfg5GHFs1Ju9gzTa3CNIZb1GToMJSDcI/EBfIPNjyCHiZrlskvJd5PRy05c1y4mJNW9R6EeBiXhaM55vDCi+RKQE0CRzYZBDH/hmvEq+7ODSMe79QCMZdXJ4RlO8olBuLo/9wCWXP1zBH+KSywBY4TF1LF2GII38nQIGstBdqC7I8Rm9YhCPLE1KB4VmYFPMrcHcimAgwEj1wP9jG68OVVUl6JvbnySxnFdnBZbVNkeUkZpPDzJBrFKlkphuAEkUpDxMFuyGFbAjAjPOK4DC0w4kiiTLfH1AXol1P1QB+PNGkoQNzUvpBrAbBTH+poE7q3lpRE/tJbkkUZRZ43XclQp9OV+SrvdTxvPW14B+b4ecG5RvCyJFliy3sVSQzcD92RE9IFQRYM3AU30N7jdvFwi+PCm659mL/57o/hOH8mvhdjoeJ0Dxa1Buw+27GJ3/XkG5jMEtGgR+uGIxt3iLY2xWknK1JGaD9nGQLVqRoFo8sx4SQbSoEEGzeBdBsngXQXHqnQVYoigOelnJ1qiXjhfHvKxF0mQnY9tU9JisiGFPnOXIiC7X92l7PMsD8u1EdP5fg97EicGnCHpzo1MWNOdWSyecFaKytlk1slkiWsyNK62vgeVmAMnOHjbL5c6mAskvK7B8cdr9swaT1v9GNEkDnc8TTsbRJUR+MhiML/2T6F3v6BcB3LRakABW3BrJJ+961o1vQ378SdQjuDLlB7tp+UmxiTZSYU8bJr4Y39s92FrOguTRnzYKvXm/m9Y1bC9JxWL+mBfxhxscHLDEQh5b4PIbuz4lMhBpQsmQHXScFUkqXZzpxz3mM2JHw7VN7alJepo4iQdZoyUM2EXNtl5S7d7SxNXFeOAUyspJ/JC0RJy+Us0ZFukU2H+S2aE5zI5vRuRSTrMsi6TDiIFez4OL9eFe10LS9zLgm4dxp3d7OHE/hnDsHU7u4IgQEvrhxILc/MSKXHdFRC0F89ioKUTKimIsSmAxpJJfMDPTUUoXj/AJBJ8g+VbgF9f4iAjGKCglz3f8uU+vPzyY9EhTKV2O6Punu2dxfaSUBh2lBMfy5yYcXMFdCn714n2NXbvY4/pF6cEiNw2gnUUuGyilriJXv4M9ytG3c9wYvusr5Hd/KaSJWcKwmIPipuzz7/wIKKqkBB5y5OwObjwUg5YvEeP9VwwCzYcFOeZPwlqWE7otyhJFT1d36pk/s74ns7ZPFadPoe3JeX0Ruv7BJrpufWnKniGDv5G2Jzn4u+p6cqBdmp6x3L1UfdPx7/eRDNzKIR/hCki1/f56/EVB6puHnO9jVN53fD+e984uZ/IPSORnBOxGKZ8JvZFEroVp/wE="
    },
    "D:\\ZG\\ZG6000\\src\\server\\ZGSTStraySystem\\ZGSTStraySystem.vcxproj.filters": {
      "before": "5Vhdb6JAFO3zJvuyv4Dwzjd+FbQRxK4PmzRx92XTZIPDWGhghp0ZGpvN/vcdqjIirbUf2jZrwMQR7rlzLufcq/aXkxP3bJGl0g0kNMGoLxuqLksQARwl6KovF2yudOWzwedP7gXB1xAwiV+OaF+OGctPNY2CGGYhVbMEEEzxnKkAZ1oEb2CKc0i0jM6KJI00U9ctmYeRJHfCYHZOcJHffeQL4yRlkEgTBNIign35HCJIQgYjiX8D6fI2ib/cHyj5XcBJBBFL5gkkgz8dIxh1+aEMfa+n2H7QUzwvMJRA1z1LN2w7MIy/rta4sczkLmSwYBCVe6cDkOcOcMBi4WQYOLETwbmDo9RJ+EkgdVxt4+JlAFdbJv8fbOUrDCNepcdL0rN6vZbV1ZVuzxsp9ky3lW438JS22Rp7QcsMvLG3b0liJ+ZFiXlJ4sxJEK8FAs6CRs8oxUfPf4oLAuAe/Ntjv2N1xobid4YtxbY6bWWot9uKObTMUadlDs3xeF/+hSQ2xRDn184sZE5Is/JcPKMaH3g3rlZ3sKah+amPs5x7l/C0n+fT7/7I90iIQKxyWjdcbWUhm5Rs+4pWhVw7TbVQxwhQdIETxG34YBBT7peYHAhAVS/5kQB4WTI2ZSS8nd5S3jEOhLeFMjkOzDd0dUigQxKWhQl6ce57SKigDGdeOTvUH3AhorgpoU2Pb0pIhKxEJJbqKJWMDgmyEtJrQKSr4Uns4mEhPRlvHbxibb0g0BoyejnIQ6XZ8IRSRk8GEnGr/YilrR0thfRSiD2e9mbDWNaPv7Ms3/bCSz6fluevPbrK1jDdVMUaumJjvSC42JVL9J6SqeVSafjeVnhcXt42lxotu9r3cUl5y0zqlIgx48HOfGRq3kFGdYp2zBRHpuZVM7nP+ken3HD50dZ1faf95gTyn0VU5QP9jDWnkUd5EeCV+YolYb+P5SMs+JUTqjcuV1v9BTT4Bw==",
      "after": "5Vdbb5swGO3ztP0HxLu551aTVIFAlodKnbK9TJEmYpxCBTazTZVqmvbXZ9pcSOiUNF0mVUP2Awa+8/k7Psfm1/uLC/dqmWfKPWY8paSvmpqhKpggGqfktq+WYgG66tXgwzv3htE7jIQiXye8ryZCFJe6zlGC84hreYoY5XQhNERzPcb3OKMFZnrO52WaxbplGLYqwyiKOxE4HzNaFo+3ciBMM4GZMiEoK2PcV8eYYBYJHCvyCeZPnynycr+Q9HuJJzEmIl2kmA1+dMxg1JUNDH2vBxw/6AHPC0wQGIZnG6bjBKb509UbH1aZPIYMlgKTau58gIoCIoiWS5hTBBMY4wWkcQZT2Rnm0NVrLz8FcPWn5P+DqXzEUSxZOkxJz+71WnbXAN2eNwLO3HBAtxt4oG21Qi9oWYEXesdSksBEkpJISpIcpkRyQRBc8vgEKt56/lNaMoSPqL8T+h27E5rA7wxbwLE7bTA02m1gDW1r1GlZQysMj63/VhJ1MSTFHZxHAkY8r/ryBDbe8GxcfdfBmobmZz7NC+ldW0/7Op5+9ke+xyKCEk2WteZqKwupl2TfV/RNyLXTbAZ2MQIS39CUSBs+G8RU+iVlZwLQtJlsKcKzqmJTwaKH6QOXO8aZ8PZQJv8G5prcnhPonAXLo5S8OvfDEvokrinaXdpb+SRN8dTdvSGex2Br4TwTeSOavx14JZVXhfWz1cFoW40/i+SlSJvg6+psBrZoDYm8GuQZCmpKr8TxUohDDK885PSwh1esX3JBc6867W5rN7qUPiZb2zCMmciLfVebyZNm1b8VDMsdlmtyb5iL5vLeOxI3VngNfMPkKfnE50pot36uvvqbGPwG"
    },
    "D:\\ZG\\ZG6000\\src\\server\\ZGSTStraySystem\\ZGSTStraySystem.vcxproj.user": {
      "before": "tZE9a8MwEIbTtdD/IETAyVDL/aCUYiVDCl06JG3obstnW0XWGekUUsgf7r+o7OItYzvee8f7PHDfF7NZvj52hh3AeY1W8ps04wyswkrbRvJA9fUjX6+uLvOtw09QxPaIxn9M95vgHFjiLLZYL3lL1D8J4VULXeHTTiuHHmtKFXaiggMY7MGJzpdBm0rcZtkdj+2MDf1xQ18vDkPPxLnwtSjBSL6jdyCKfp6zDdpK06iezBdxqnUTXDEky9N8sTUF1ei6ZSJl8gxlaE7Hh/vkFxmhO9pjUO3EHqExFufyXExno+I/CL6BgcLDXyqO0sPfVj8=",
      "after": "tZHNSsNAFIXrVvAdhqGQdmEm/iAimXRRwY2LVov7ZHKTjMzkhpk7pUJf1XdxEulGXOrynns454PzeTab5auDNWwPzmvsJb9KM86gV1jrvpU8UHN5z1fFxXm+cfgOitgO0fi3k38dnIOeOIspvZe8IxoehPCqA1v61Grl0GNDqUIratiDwQGcsL4K2tTiOstueExnbMyPH/p4chgGJn4Tn8sKjORbegWiyOc5W2Nfa5rQk/kiXo1ugytHZXmcLzampAadXSZSJo9QhfZ4uLtNvitj6ZZ2GFR36i5y8VOZ2MTJMMH9A9oLGCg9/A1cPuKOWxVf"
    }
  }
}]]></FlowDocument.Tag>
  <Section Margin="0,24" TextAlignment="Center">
    <Paragraph FontSize="24" FontWeight="Bold" Margin="12,0">
      <LineBreak />
      <Span Foreground="Gray">Qt Visual Studio Tools</Span>
    </Paragraph>
    <Paragraph FontSize="42" Margin="12,0" FontWeight="Bold">
      <Span TextDecorations="Underline">Project Format Conversion</Span>
    </Paragraph>
    <Paragraph Margin="12,8" FontSize="18">
      <Span>Report generated on 2025-04-30 16:01:21</Span>
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Files</Span>
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.user]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.user?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.user?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.user?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.user?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.user?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Changes</Span>
    </Paragraph>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "$(QTDIR)"]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;.;..\..\include;..\..\include\thirdparty;..\..\ice;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include\QtNetwork]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include\QtCore]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystem\moc;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;.;..\..\include;..\..\include\thirdparty;..\..\ice;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtConcurrent]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtNetwork]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystem\moc;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>D:\ZG\ZG6000\lib\ZGRuntime.lib;D:\ZG\ZG6000\lib\ZGPubFun.lib;D:\ZG\ZG6000\lib\ZGServerBase.lib;D:\ZG\ZG6000\lib\ZGRedisClient.lib;D:\ZG\ZG6000\lib\ZGJson.lib;D:\ZG\ZG6000\lib\ZGServerApplication.lib;D:\ZG\ZG6000\lib\ZGDebugMng.lib;D:\ZG\ZG6000\lib\ZGHeartMng.lib;D:\ZG\ZG6000\lib\ZGProxyMng.lib;D:\ZG\ZG6000\lib\ZGProxyCommon.lib;D:\ZG\ZG6000\lib\ZGMqttClient.lib;E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\lib\Qt6Concurrent.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\lib\Qt6Network.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\lib\Qt6Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>D:\ZG\ZG6000\lib\ZGRuntime.lib;D:\ZG\ZG6000\lib\ZGPubFun.lib;D:\ZG\ZG6000\lib\ZGServerBase.lib;D:\ZG\ZG6000\lib\ZGRedisClient.lib;D:\ZG\ZG6000\lib\ZGJson.lib;D:\ZG\ZG6000\lib\ZGServerApplication.lib;D:\ZG\ZG6000\lib\ZGDebugMng.lib;D:\ZG\ZG6000\lib\ZGHeartMng.lib;D:\ZG\ZG6000\lib\ZGProxyMng.lib;D:\ZG\ZG6000\lib\ZGProxyCommon.lib;D:\ZG\ZG6000\lib\ZGMqttClient.lib;E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt6Concurrent]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt6Network]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt6Core]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;.;..\..\include;..\..\include\thirdparty;..\..\ice;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include\QtConcurrent]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include\QtNetwork]]></Span><Span><![CDATA[;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\include\QtCore]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystemd\moc;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;.;..\..\include;..\..\include\thirdparty;..\..\ice;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtConcurrent]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtNetwork]]></Span><Span><![CDATA[;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystemd\moc;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>ZGRuntimed.lib;ZGPubFund.lib;ZGServerBased.lib;D:\ZG\ZG6000\lib\ZGRedisClientd.lib;ZGJsond.lib;ZGServerApplicationd.lib;ZGDebugMngd.lib;ZGHeartMngd.lib;ZGProxyMngd.lib;ZGProxyCommond.lib;ZGMqttClientd.lib;E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\lib\Qt6Concurrentd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\lib\Qt6Networkd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>ZGRuntimed.lib;ZGPubFund.lib;ZGServerBased.lib;D:\ZG\ZG6000\lib\ZGRedisClientd.lib;ZGJsond.lib;ZGServerApplicationd.lib;ZGDebugMngd.lib;ZGHeartMngd.lib;ZGProxyMngd.lib;ZGProxyCommond.lib;ZGMqttClientd.lib;E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt6Concurrentd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt6Networkd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt6Cored]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTCDCBranch.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTCDCBranch.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTCDCBranch.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTCDCBranch.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTEndPoint.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTEndPoint.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTEndPoint.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTEndPoint.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTSensor.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTSensor.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTSensor.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTSensor.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTStraySystemMng.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTStraySystemMng.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTStraySystemMng.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTStraySystemMng.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTSystem.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTSystem.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTSystem.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTSystem.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">E:\Qt\6]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[5.3\msvc2019_64\bin\moc.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-IE:/Qt/6.5.3/msvc2019_64/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -IE:/Library/ice-3.7.6/include -ID:/ZG/ZG6000/src/server/ZGSTStraySystem -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtConcurrent]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtNetwork]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -Zi -MDd -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightCoral"><![CDATA[E:\Qt\6.5.3\msvc2019_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -Zi -MDd -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "."]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/ZG/ZG6000/src/server/ZGSTStraySystem ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span Background="LightCoral"><![CDATA[ -ID:/ZG/ZG6000/src/server/ZGSTStraySystem]]></Span><Span><![CDATA[ ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-IE:/Library/ice-3.7.6/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I. ]]></Span><Span><![CDATA[-ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting custom build steps to Qt/MSBuild items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include]]></Span><Span Background="LightCoral"><![CDATA[;.;..]]></Span><Span><![CDATA[\..\include;..\..\include\thirdparty;..\..\ice;$(QTDIR)\include;$(QTDIR)\include\QtConcurrent;$(QTDIR)\include\QtNetwork;$(QTDIR)\include\QtCore;..\..\..\tmp\ZGSTStraySystem\moc;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include]]></Span><Span Background="LightGreen"><![CDATA[;..]]></Span><Span><![CDATA[\..\include;..\..\include\thirdparty;..\..\ice;$(QTDIR)\include;$(QTDIR)\include\QtConcurrent;$(QTDIR)\include\QtNetwork;$(QTDIR)\include\QtCore;..\..\..\tmp\ZGSTStraySystem\moc;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>.;$(QTDIR)/mkspecs/win32-msvc;E:/Library/ice-3.7.6/include;D:/ZG/ZG6000/src/include;D:/ZG/ZG6000/src/include/thirdparty;D:/ZG/ZG6000/src/ice;$(QTDIR)/include;$(QTDIR)/include/QtConcurrent;$(QTDIR)/include/QtNetwork;$(QTDIR)/include/QtCore;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include]]></Span><Span Background="LightCoral"><![CDATA[;.;..]]></Span><Span><![CDATA[\..\include;..\..\include\thirdparty;..\..\ice;$(QTDIR)\include;$(QTDIR)\include\QtConcurrent;$(QTDIR)\include\QtNetwork;$(QTDIR)\include\QtCore;..\..\..\tmp\ZGSTStraySystemd\moc;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include]]></Span><Span Background="LightGreen"><![CDATA[;..]]></Span><Span><![CDATA[\..\include;..\..\include\thirdparty;..\..\ice;$(QTDIR)\include;$(QTDIR)\include\QtConcurrent;$(QTDIR)\include\QtNetwork;$(QTDIR)\include\QtCore;..\..\..\tmp\ZGSTStraySystemd\moc;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>.;$(QTDIR)/mkspecs/win32-msvc;E:/Library/ice-3.7.6/include;D:/ZG/ZG6000/src/include;D:/ZG/ZG6000/src/include/thirdparty;D:/ZG/ZG6000/src/ice;$(QTDIR)/include;$(QTDIR)/include/QtConcurrent;$(QTDIR)/include/QtNetwork;$(QTDIR)/include/QtCore;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTCDCBranch.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTCDCBranch.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalInputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTCDCBranch]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="ZGSTEndPoint]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<Command]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[exe  -DUNICODE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="ZGSTSensor]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h"]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[/>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC ZGSTCDCBranch.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTCDCBranch.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTCDCBranch.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC ZGSTCDCBranch.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="ZGSTEndPoint.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTEndPoint.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC ZGSTEndPoint.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTEndPoint.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTEndPoint.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC ZGSTEndPoint.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="ZGSTSensor.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTSensor.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC ZGSTSensor.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTSensor.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSensor.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC ZGSTSensor.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTStraySystemMng.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTStraySystemMng.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalInputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTStraySystemMng]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="ZGSTSystem]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC ZGSTStraySystemMng.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTStraySystemMng.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTStraySystemMng.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC ZGSTStraySystemMng.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="ZGSTSystem.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGSTSystem.h;D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DNDEBUG -DQT_NO_DEBUG -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystem/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC ZGSTSystem.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGSTSystem.h;D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DICE_CPP11_MAPPING -DQT_CONCURRENT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/ZG/ZG6000/tmp/ZGSTStraySystemd/moc/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -IE:/Library/ice-3.7.6/include -I. -ID:/ZG/ZG6000/src/include -ID:/ZG/ZG6000/src/include/thirdparty -ID:/ZG/ZG6000/src/ice -I$(QTDIR)/include -I$(QTDIR)/include/QtConcurrent -I$(QTDIR)/include/QtNetwork -I$(QTDIR)/include/QtCore -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include" -I"D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt" ZGSTSystem.h -o D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC ZGSTSystem.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj.filters]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTCDCBranch.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTCDCBranch.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTEndPoint.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTEndPoint.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTSensor.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTSensor.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTStraySystemMng.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTStraySystemMng.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="ZGSTSystem.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="ZGSTSystem.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTCDCBranch.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTCDCBranch.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTEndPoint.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTEndPoint.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTSensor.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTSensor.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTStraySystemMng.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTStraySystemMng.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystem\moc\moc_ZGSTSystem.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="..\..\..\tmp\ZGSTStraySystemd\moc\moc_ZGSTSystem.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Enabling multi-processor compilation]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Project format version]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<Keyword>Qt4VSv1.0</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<Keyword>QtVS_v304</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Fallback for QTMSBUILD environment variable]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Default Qt properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt build settings]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Warn if Qt/MSBuild is not found]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </Target>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt property sheet]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt targets]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Copying Qt build reference to QtInstall project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  ]]></Span><Span Background="LightCoral"><![CDATA[<PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtInstall>6.5.3_msvc2019_64</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtInstall>6.5.3_msvc2019_64</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;]]></Span><Span Background="LightCoral"><![CDATA[QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module include paths from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\include;$(QTDIR)\include\QtConcurrent;$(QTDIR)\include\QtNetwork;$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystem\moc]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystem\moc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\include;$(QTDIR)\include\QtConcurrent;$(QTDIR)\include\QtNetwork;$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystemd\moc]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice]]></Span><Span><![CDATA[;..\..\..\tmp\ZGSTStraySystemd\moc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module libraries from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>D:\ZG\ZG6000\lib\ZGRuntime.lib;D:\ZG\ZG6000\lib\ZGPubFun.lib;D:\ZG\ZG6000\lib\ZGServerBase.lib;D:\ZG\ZG6000\lib\ZGRedisClient.lib;D:\ZG\ZG6000\lib\ZGJson.lib;D:\ZG\ZG6000\lib\ZGServerApplication.lib;D:\ZG\ZG6000\lib\ZGDebugMng.lib;D:\ZG\ZG6000\lib\ZGHeartMng.lib;D:\ZG\ZG6000\lib\ZGProxyMng.lib;D:\ZG\ZG6000\lib\ZGProxyCommon.lib;D:\ZG\ZG6000\lib\ZGMqttClient.lib;E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\lib\Qt6Concurrent.lib;$(QTDIR)\lib\Qt6Network.lib;$(QTDIR)\lib\Qt6Core.lib;]]></Span><Span><![CDATA[%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>D:\ZG\ZG6000\lib\ZGRuntime.lib;D:\ZG\ZG6000\lib\ZGPubFun.lib;D:\ZG\ZG6000\lib\ZGServerBase.lib;D:\ZG\ZG6000\lib\ZGRedisClient.lib;D:\ZG\ZG6000\lib\ZGJson.lib;D:\ZG\ZG6000\lib\ZGServerApplication.lib;D:\ZG\ZG6000\lib\ZGDebugMng.lib;D:\ZG\ZG6000\lib\ZGHeartMng.lib;D:\ZG\ZG6000\lib\ZGProxyMng.lib;D:\ZG\ZG6000\lib\ZGProxyCommon.lib;D:\ZG\ZG6000\lib\ZGMqttClient.lib;E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib;]]></Span><Span><![CDATA[%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>ZGRuntimed.lib;ZGPubFund.lib;ZGServerBased.lib;D:\ZG\ZG6000\lib\ZGRedisClientd.lib;ZGJsond.lib;ZGServerApplicationd.lib;ZGDebugMngd.lib;ZGHeartMngd.lib;ZGProxyMngd.lib;ZGProxyCommond.lib;ZGMqttClientd.lib;E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\lib\Qt6Concurrentd.lib;$(QTDIR)\lib\Qt6Networkd.lib;$(QTDIR)\lib\Qt6Cored.lib;]]></Span><Span><![CDATA[%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalDependencies>ZGRuntimed.lib;ZGPubFund.lib;ZGServerBased.lib;D:\ZG\ZG6000\lib\ZGRedisClientd.lib;ZGJsond.lib;ZGServerApplicationd.lib;ZGDebugMngd.lib;ZGHeartMngd.lib;ZGProxyMngd.lib;ZGProxyCommond.lib;ZGMqttClientd.lib;E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib;]]></Span><Span><![CDATA[%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt lib path from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;D:\ZG\ZG6000\lib]]></Span><Span><![CDATA[;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>D:\ZG\ZG6000\lib]]></Span><Span><![CDATA[;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;D:\ZG\ZG6000\lib]]></Span><Span><![CDATA[;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>D:\ZG\ZG6000\lib]]></Span><Span><![CDATA[;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from resource compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;]]></Span><Span Background="LightCoral"><![CDATA[QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Adding Qt module names to QtModules project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core;network;concurrent</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core;network;concurrent</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting OutputFile to <tool>Dir and <tool>FileName]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing old properties from project items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\ZG\ZG6000\src\server\ZGSTStraySystem\ZGSTStraySystem.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>D:\ZG\ZG6000\tmp\ZGSTStraySystem\moc\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>.;$(QTDIR)/mkspecs/win32-msvc;E:/Library/ice-3.7.6/include;D:/ZG/ZG6000/src/include;D:/ZG/ZG6000/src/include/thirdparty;D:/ZG/ZG6000/src/ice;$(QTDIR)/include;$(QTDIR)/include/QtConcurrent;$(QTDIR)/include/QtNetwork;$(QTDIR)/include/QtCore;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>D:\ZG\ZG6000\tmp\ZGSTStraySystemd\moc\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;QT_CONCURRENT_LIB;QT_NETWORK_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>.;$(QTDIR)/mkspecs/win32-msvc;E:/Library/ice-3.7.6/include;D:/ZG/ZG6000/src/include;D:/ZG/ZG6000/src/include/thirdparty;D:/ZG/ZG6000/src/ice;$(QTDIR)/include;$(QTDIR)/include/QtConcurrent;$(QTDIR)/include/QtNetwork;$(QTDIR)/include/QtCore;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\ATLMFC\include;D:\Microsoft Visual Studio\2019\Enterprise\VC\Tools\MSVC\14.29.30133\include;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\ucrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\um;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\winrt;C:\Program Files (x86)\Windows Kits\10\include\10.0.19041.0\cppwinrt</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
  </Section>
  <Section>
    <Paragraph />
  </Section>
</FlowDocument>
<!--DECjVYgJnxi+X91sJyST1I9mr7FmVOdfqfpKibHpVfs=-->
