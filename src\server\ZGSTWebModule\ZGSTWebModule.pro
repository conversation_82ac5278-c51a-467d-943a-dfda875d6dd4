win32:CONFIG(release, debug|release): TARGET = ZGSTWebModule
else:win32:CONFIG(debug, debug|release): TARGET = ZGSTWebModuled

win32:QMAKE_CXXFLAGS += /bigobj

DEFINES += ICE_CPP11_MAPPING

PLATFORM_ROOT=$$(ZG6000)/
PLATFORM_BIN=$$(ZG6000)/bin/
PLATFORM_CONF=$$(ZG6000)/conf/
PLATFORM_LIB=$$(ZG6000)/lib/
PLATFORM_SRC=$$(ZG6000)/src/
PLATFORM_TMP=$$(ZG6000)/tmp/

INCLUDEPATH += $$(ICE_HOME)/include
INCLUDEPATH += . $${PLATFORM_SRC}/include
INCLUDEPATH += $${PLATFORM_SRC}/include/thirdparty
INCLUDEPATH += $${PLATFORM_SRC}/ice/

LIBS += -L$${PLATFORM_LIB} -L$${PLATFORM_BIN}
LIBS += -L$$(ICE_HOME)/lib/x64/Release
LIBS += -L$$(ICE_HOME)/lib/x64/Debug

win32{
    DESTDIR = $${PLATFORM_LIB}
    DLLDESTDIR = $${PLATFORM_BIN}
}

unix{
    DESTDIR = $${PLATFORM_BIN}
}

COMPLIER_TEMP = $${PLATFORM_TMP}
OBJECTS_DIR = $${COMPLIER_TEMP}/$$TARGET/obj
MOC_DIR = $${COMPLIER_TEMP}/$$TARGET/moc
UI_DIR = $${COMPLIER_TEMP}/$$TARGET/uic

QT += network concurrent httpserver

TEMPLATE = lib
CONFIG += plugin

CONFIG += c++11

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    ../../ice/ZGSTStraySystem.cpp \
    ../../ice/ZGServerBase.cpp \
    ZGSTHandle.cpp \
    ZGSTWebModule.cpp

HEADERS += \
    ../../ice/ZGSTStraySystem.h \
    ../../ice/ZGServerBase.h \
    ZGSTHandle.h \
    ZGSTWebModule.h

DISTFILES += ZGSTWebModule.json

win32|unix{
    CONFIG(release, debug|release){
        LIBS += -lZGPubFun -lZGRuntime -lZGDebugMng -lZGProxyMng -lZGProxyCommon -lZGWebModule -lZGJson
    }

    CONFIG(debug, debug|release){
        LIBS += -lZGPubFund -lZGRuntimed -lZGDebugMngd -lZGProxyMngd -lZGProxyCommond -lZGWebModuled -lZGJsond
    }
}

win32{
    CONFIG(release, debug|release){
        LIBS += -lice37++11
    }

    CONFIG(debug, debug|release){
        CONFIG += console
        LIBS += -lice37++11d
    }
}

unix{
    target.path = /usr/lib
    INSTALLS += target

    LIBS += -lIce++11 -lIceGrid

    CONFIG(release, debug|release){
    }

    CONFIG(debug, debug|release){
        CONFIG += console
    }
}
