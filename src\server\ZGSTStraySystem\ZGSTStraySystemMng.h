﻿#ifndef ZG6000_ZGSTSTRAYSYSTEMMNG_H
#define ZG6000_ZGSTSTRAYSYSTEMMNG_H

#include <QObject>
#include "ZGServerCommon.h"

class ZGRedisClient;
class ZGSTSystem;
namespace ZG6000 {

class ZGSTStraySystemMng : public QObject
{
    Q_OBJECT

public:
    static ZGSTStraySystemMng* instance();
    void init();
    bool checkState();
    bool setMeasureStation(std::string clientID, std::string inStationID, std::string outStationID, ErrorInfo& e);
    bool calculateOffset(ErrorInfo& e);
    bool startCalculate(ErrorInfo& e);
    bool stopCalculate(ErrorInfo& e);
    bool getValidStations(ListStringMap& listMapStation, ErrorInfo& e);
    bool getSystemParam(StringMap& systemParam, ErrorInfo& e);
    bool setSystemParam(StringMap systemParam, ErrorInfo& e);

public:
    bool sendPLCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value,
        bool automatic, ZG6000::ErrorInfo& e);
    bool sendYkCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value,
        bool automatic, ErrorInfo& e);
    bool sendYsCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName,
        const std::string& value, bool automatic, ErrorInfo& e);
    bool sendCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName,
        const std::string& value, bool automatic, ErrorInfo& e);
private:
    explicit ZGSTStraySystemMng(QObject *parent = nullptr);
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRedisClient();
    bool initSystem();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    int m_initInterval{10};
    ZGRedisClient* m_pRedisClient{nullptr};
    ZGSTSystem* m_pSystem{nullptr};
};

inline static ZGSTStraySystemMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGSTSTRAYSYSTEMMNG_H
