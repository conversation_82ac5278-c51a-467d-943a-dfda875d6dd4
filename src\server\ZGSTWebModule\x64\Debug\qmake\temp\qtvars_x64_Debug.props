<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_GUI_LIB;QT_NETWORK_LIB;QT_CONCURRENT_LIB;QT_CORE_LIB</Qt_DEFINES_>
<Qt_INCLUDEPATH_>C:\Users\<USER>\AppData\Local\Temp\kxvu3ocm.v3q;D:\Qt\6.2.4\msvc2019_64\include;D:\Qt\6.2.4\msvc2019_64\include\QtGui;D:\Qt\6.2.4\msvc2019_64\include\QtNetwork;D:\Qt\6.2.4\msvc2019_64\include\QtConcurrent;D:\Qt\6.2.4\msvc2019_64\include\QtCore;C:\Users\<USER>\AppData\Local\Temp\kxvu3ocm.v3q;\include;D:\Qt\6.2.4\msvc2019_64\mkspecs\win32-msvc</Qt_INCLUDEPATH_>
<Qt_STDCPP_>stdcpp17</Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDebugDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -utf-8</Qt_CL_OPTIONS_>
<Qt_LIBS_>D:\Qt\6.2.4\msvc2019_64\lib\Qt6Guid.lib;D:\Qt\6.2.4\msvc2019_64\lib\Qt6Networkd.lib;D:\Qt\6.2.4\msvc2019_64\lib\Qt6Concurrentd.lib;D:\Qt\6.2.4\msvc2019_64\lib\Qt6Cored.lib;D:\Qt\6.2.4\msvc2019_64\lib\Qt6EntryPointd.lib;shell32.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>D:/Qt/6.2.4/msvc2019_64</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>D:/Qt/6.2.4/msvc2019_64</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>D:/Qt/6.2.4/msvc2019_64</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>D:/Qt/Docs/Qt-6.2.4</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>D:/Qt/6.2.4/msvc2019_64/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>D:/Qt/6.2.4/msvc2019_64/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>D:/Qt/6.2.4/msvc2019_64/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>D:/Qt/6.2.4/msvc2019_64/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>D:/Qt/6.2.4/msvc2019_64/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>D:/Qt/6.2.4/msvc2019_64/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_QML_>D:/Qt/6.2.4/msvc2019_64/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>D:/Qt/6.2.4/msvc2019_64/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>D:/Qt/Examples/Qt-6.2.4</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>D:/Qt/Examples/Qt-6.2.4</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>D:/Qt/6.2.4/msvc2019_64</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>D:/Qt/6.2.4/msvc2019_64</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>D:/Qt/6.2.4/msvc2019_64/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBEXECS_>D:/Qt/6.2.4/msvc2019_64/bin</QMake_QT_HOST_LIBEXECS_>
<QMake_QT_HOST_LIBS_>D:/Qt/6.2.4/msvc2019_64/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.1</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>6.2.4</QMake_QT_VERSION_>
<Qt_INCLUDEPATH_
      >$(Qt_INCLUDEPATH_);D:\ZG\ZG6000\tmp\ZGSTWebModuled\moc</Qt_INCLUDEPATH_>
    <QtBkup_QtInstall
      >6.2.4_msvc2019_64</QtBkup_QtInstall>
    <QtBkup_QtModules
      >core;network;gui;concurrent</QtBkup_QtModules>
    <QtBkup_QtPathBinaries
      >bin</QtBkup_QtPathBinaries>
    <QtBkup_QtPathLibraryExecutables
      >bin</QtBkup_QtPathLibraryExecutables>
    <QtBkup_QtHeaderSearchPath
      ></QtBkup_QtHeaderSearchPath>
    <QtBkup_QtLibrarySearchPath
      ></QtBkup_QtLibrarySearchPath>
    <QtBkup_QtVars
      >DEFINES=/Project/ItemDefinitionGroup/ClCompile/PreprocessorDefinitions;INCLUDEPATH=/Project/ItemDefinitionGroup/ClCompile/AdditionalIncludeDirectories;STDCPP=/Project/ItemDefinitionGroup/ClCompile/LanguageStandard;RUNTIME=/Project/ItemDefinitionGroup/ClCompile/RuntimeLibrary;CL_OPTIONS=/Project/ItemDefinitionGroup/ClCompile/AdditionalOptions;LIBS=/Project/ItemDefinitionGroup/Link/AdditionalDependencies;LINK_OPTIONS=/Project/ItemDefinitionGroup/Link/AdditionalOptions</QtBkup_QtVars>
    <QtBkup_QMakeCodeLines
      ></QtBkup_QMakeCodeLines>
    <QtBkup_QtBuildConfig
      >debug</QtBkup_QtBuildConfig>
    <QtVersion>6.2.4</QtVersion>
    <QtVersionMajor>6</QtVersionMajor>
    <QtVersionMinor>2</QtVersionMinor>
    <QtVersionPatch>4</QtVersionPatch>
  </PropertyGroup>
</Project>
