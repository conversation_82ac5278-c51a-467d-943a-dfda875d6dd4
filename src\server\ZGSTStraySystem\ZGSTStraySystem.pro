win32:CONFIG(release, debug|release): TARGET = ZGSTStraySystem
else:win32:CONFIG(debug, debug|release): TARGET = ZGSTStraySystemd

DEFINES += ICE_CPP11_MAPPING

PLATFORM_ROOT=$$(ZG6000)/
PLATFORM_BIN=$$(ZG6000)/bin/
PLATFORM_CFG=$$(ZG6000)/cfg/
PLATFORM_LIB=$$(ZG6000)/lib/
PLATFORM_SRC=$$(ZG6000)/src/
PLATFORM_TMP=$$(ZG6000)/tmp/

INCLUDEPATH += $$(ICE_HOME)/include
INCLUDEPATH += . $${PLATFORM_SRC}/include
INCLUDEPATH += $${PLATFORM_SRC}/include/thirdparty
INCLUDEPATH += $${PLATFORM_SRC}/ice/

win32{
    LIBS += -L$${PLATFORM_LIB}
}
unix{
    LIBS += -L$${PLATFORM_BIN}
}
LIBS += -L$$(ICE_HOME)/lib/x64/Release
LIBS += -L$$(ICE_HOME)/lib/x64/Debug

DESTDIR = $${PLATFORM_BIN}

COMPLIER_TEMP = $${PLATFORM_TMP}
OBJECTS_DIR = $${COMPLIER_TEMP}/$$TARGET/obj
MOC_DIR = $${COMPLIER_TEMP}/$$TARGET/moc
UI_DIR = $${COMPLIER_TEMP}/$$TARGET/uic

QT -= gui
QT += concurrent
QT += network

CONFIG += c++17 console
CONFIG -= app_bundle

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
        ../../ice/ZGSTStraySystem.cpp \
        ZGSTCDCBranch.cpp \
        ZGSTEndPoint.cpp \
        ZGSTSensor.cpp \
        ZGSTStraySystemI.cpp \
        ZGSTStraySystemMng.cpp \
        ZGSTSystem.cpp \
        main.cpp

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

HEADERS += \
    ../../ice/ZGSTStraySystem.h \
    ZGSTCDCBranch.h \
    ZGSTEndPoint.h \
    ZGSTSensor.h \
    ZGSTStraySystemI.h \
    ZGSTStraySystemMng.h \
    ZGSTSystem.h

win32|unix:{
    CONFIG(release, debug|release){
        LIBS += -lZGRuntime -lZGPubFun -lZGServerBase -lZGRedisClient -lZGJson  \
        -lZGServerApplication -lZGDebugMng -lZGHeartMng -lZGProxyMng -lZGProxyCommon -lZGMqttClient
    }

    CONFIG(debug, debug|release){
        CONFIG += console
        LIBS += -lZGRuntimed -lZGPubFund -lZGServerBased -lZGRedisClientd -lZGJsond \
        -lZGDebugMngd -lZGServerApplicationd -lZGDebugMngd -lZGHeartMngd -lZGProxyMngd -lZGProxyCommond -lZGMqttClientd
    }
}

win32{

    CONFIG(release, debug|release){
        LIBS += -lice37++11
    }

    CONFIG(debug, debug|release){
        LIBS += -lice37++11d
    }
}

unix{
    target.path = /usr/lib
    INSTALLS += target

    LIBS += -lIce++11 -lIceGrid

    CONFIG(release, debug|release){
    }

    CONFIG(debug, debug|release){
    }
}
