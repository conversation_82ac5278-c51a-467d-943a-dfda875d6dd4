﻿#include "ZGSTStraySystemMng.h"
#include <QThread>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

#include "redis/ZGRedisClient.h"
#include "zgerror/ZGSTStraySystemError.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

#include "ZGSTSystem.h"

namespace ZG6000
{
    ZGSTStraySystemMng* ZGSTStraySystemMng::instance()
    {
        if (g_pInstance == nullptr)
            g_pInstance = new ZGSTStraySystemMng;
        return g_pInstance;
    }

    void ZGSTStraySystemMng::init()
    {
        ZGLOG_TRACE("ZGSTStraySystem init......");
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::sleep(m_initInterval);
        }
        while (!initRedisClient())
        {
            ZGLOG_ERROR("initRedisClient error.");
            QThread::sleep(m_initInterval);
        }
        while (!initSystem())
        {
            ZGLOG_ERROR("initSystem error.");
            QThread::sleep(m_initInterval);
        }
        m_initialized = true;
    }

    bool ZGSTStraySystemMng::checkState()
    {
        return m_initialized;
    }

    bool ZGSTStraySystemMng::setMeasureStation(std::string clientID, std::string inStationID, std::string outStationID, ErrorInfo& e)
    {
        return m_pSystem->setMeasureStation(clientID, inStationID, outStationID, e);
    }

    bool ZGSTStraySystemMng::calculateOffset(ErrorInfo& e)
    {
        return m_pSystem->calculateOffset(e);
    }

    bool ZGSTStraySystemMng::startCalculate(ErrorInfo& e)
    {
        return m_pSystem->startCalculate(e);
    }

    bool ZGSTStraySystemMng::stopCalculate(ErrorInfo& e)
    {
        return m_pSystem->stopCalculate(e);
    }

    bool ZGSTStraySystemMng::getValidStations(ListStringMap& listMapStation, ErrorInfo& e)
    {
        return m_pSystem->getValidStations(listMapStation, e);
    }

    bool ZGSTStraySystemMng::getSystemParam(StringMap& systemParam, ErrorInfo& e)
    {
        return m_pSystem->getSystemParam(systemParam, e);
    }

    bool ZGSTStraySystemMng::setSystemParam(StringMap systemParam, ErrorInfo& e)
    {
        return m_pSystem->setSystemParam(systemParam, e);
    }

    bool ZGSTStraySystemMng::sendPLCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ZG6000::ErrorInfo& e)
    {
        return sendYkCommand(clientID, deviceID, propertyName, value, automatic, e);
    }

    bool ZGSTStraySystemMng::sendYkCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ErrorInfo& e)
    {
        return sendCommand(clientID, deviceID, propertyName, value, automatic, e);
    }

    bool ZGSTStraySystemMng::sendYsCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ErrorInfo& e)
    {
        return sendCommand(clientID, deviceID, propertyName, value, automatic, e);
    }

    bool ZGSTStraySystemMng::sendCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ErrorInfo& e)
    {
        std::string tableName;
        std::string dataID;
        if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        QJsonObject command;
        command["id"] = dataID.c_str();
        std::string modelTable;
        if (tableName == "mp_param_dataset_yk")
            modelTable = "mp_param_model_yk";
        if (tableName == "mp_param_dataset_ys")
            modelTable = "mp_param_model_ys";
        std::string sql =
            "SELECT a.isSelectCtrl FROM " + modelTable + " a LEFT JOIN " + tableName + " b ON b.dataModelID = a.id "
            "WHERE b.id = '" + dataID + "' ORDER BY a.id";
        std::string result;
        if (!ZGProxyCommon::execQuerySqlField(sql, result))
        {
            ZGLOG_ERROR("execQuerySqlField error.");
            return false;
        }
        QString commandID;
        if (tableName == "mp_param_dataset_yk")
            commandID = "ZG_DC_YK_EXEC";
        if (tableName == "mp_param_dataset_ys")
            commandID = "ZG_DC_YS_EXEC";
        if (result == "1")
        {
            if (tableName == "mp_param_dataset_yk")
                commandID = "ZG_DC_YK_SELECT";
            if (tableName == "mp_param_dataset_ys")
                commandID = "ZG_DC_YS_SELECT";
        }
        command["commandID"] = commandID;
        command["isReturnValue"] = "0";
        if (automatic)
        {
            command["srcType"] = "auto";
            command["srcID"] = "-1";
        }
        else
        {
            command["srcType"] = "client";
            command["srcID"] = clientID.c_str();
        }
        command["rtCode"] = QString::number(ZGUtils::genNumber(0, 10000));
        command["rtValue"] = value.c_str();
        command["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
        QJsonArray array;
        array.append(command);
        QJsonDocument doc(array);
        long long size;
        QString errMsg;
        std::string topicName;
        if (tableName == "mp_param_dataset_yk")
            topicName = "ZG_Q_SYSTEM_YK";
        if (tableName == "mp_param_dataset_ys")
            topicName = "ZG_Q_SYSTEM_YS";
        ZGLOG_DEBUG(doc.toJson());
        if (!m_pRedisClient->rpush(topicName.c_str(), doc.toJson(), size, errMsg))
        {
            ZGLOG_ERROR("Send command to ys queue error.");
        }
        return true;
    }

    ZGSTStraySystemMng::ZGSTStraySystemMng(QObject* parent)
        : QObject{parent}
    {
    }

    void ZGSTStraySystemMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
    }

    bool ZGSTStraySystemMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        return true;
    }

    bool ZGSTStraySystemMng::initRedisClient()
    {
        QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
        listClientType << ZGRuntime::REDIS_RT_QUEUE;
        if (!ZGRuntime::instance()->initRedisClient(listClientType))
        {
            ZGLOG_ERROR("Init redis client error.");
            return false;
        }
        m_pRedisClient = ZGRuntime::instance()->getRedisClientRTQueue();
        if (m_pRedisClient == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTQueue error.");
            return false;
        }
        return true;
    }

    bool ZGSTStraySystemMng::initSystem()
    {
        if (!m_pSystem)
            m_pSystem = new ZGSTSystem;
        return m_pSystem->initialize();
    }
} // namespace ZG6000
