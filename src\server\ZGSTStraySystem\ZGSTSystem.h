﻿#ifndef ZGSTSYSTEM_H
#define ZGSTSYSTEM_H

#include <QObject>
#include <QTimer>
#include <QTime>
#include "ZGServerCommon.h"

class ZGRedisClient;
class ZGMqttClient;
class ZGSTCDCBranch;
class ZGSTSensor;
class ZGSTSystem : public QObject
{
    Q_OBJECT
public:
    explicit ZGSTSystem(QObject *parent = nullptr);

    bool initialize();
    bool setMeasureStation(const std::string& clientID, const std::string& inStationID, const std::string& outStationID, ZG6000::ErrorInfo& e);
    bool calculateOffset(ZG6000::ErrorInfo& e);
    bool startCalculate(ZG6000::ErrorInfo& e);
    bool stopCalculate(ZG6000::ErrorInfo& e);
    bool getValidStations(ZG6000::ListStringMap& listMapStation, ZG6000::ErrorInfo& e);
    bool getSystemParam(ZG6000::StringMap& systemParam, ZG6000::ErrorInfo& e);
    bool setSystemParam(const ZG6000::StringMap& systemParam, ZG6000::ErrorInfo& e);
    int maxCalcDays() const { return m_maxCalcDays; }
    int maxWarnHours() const { return m_maxWarnHours; }
    int warnOffHours() const { return m_warnOffHours; }
    const QTime& operationStart() const { return m_operStartTime; }
    const QTime& operationEnd() const { return m_operEndTime; }
    bool autoDrainage() const { return m_autoDrainage == 2; }

signals:
    void ctrlTimer(bool start);

private slots:
    void onProcess();
    void onUpdateData();
    void onCtrlTimer(bool start);

private:
    bool initMqttClient();
    void initServerInstConfig();
    bool initPropertyDataID();
    bool initCDCBranch();
    bool updateGlobalParam();
    bool updateParamVariable(const std::string& paramName, int& variable);
    void processPL();
    void syncOperTime();
    bool calculateCurrentModel();
    bool getCurrentSensorList(const std::string& stationID, ZG6000::ListStringMap& listUpSensors, ZG6000::ListStringMap& listDownSensors);
    void sortSensorList(ZG6000::ListStringMap& listSensors);
    bool checkAndExecute(std::function<bool()> func, ZG6000::ErrorInfo& e);
    bool getDeviceByStation(const std::string& stationID, std::string& deviceID);
    bool sendCommandToDevice(const std::string& commandProp);
    bool getDatasetOfServiceInst(std::string& calcDataset);
    bool updateSensorValues(const std::string& sensorID, const std::string& prefix, double& gdVol, double& ggIa, double& ggIb);
    bool setSensorOffsetValues(const std::string& sensorID, const std::string& prefix, ZG6000::ErrorInfo& e);
    bool getYcPropertyValue(const std::string& propertyName, std::string& value);
    bool updateYcPropertyValue(const std::string& propertyName, const std::string& value, const QDateTime& dt);
    bool startMeasure(ZG6000::ErrorInfo& e);
    bool calculateResistance(double& resistance);
    enum CalculateState
    {
        CS_START, CS_EXECUTING, CS_FINISH, CS_ERROR, CS_REPORT
    };
    void publishCalculateState(CalculateState state, size_t progress, const QString& errMsg, const QString& detail = "");
    inline bool calculateTwoSensorsDistance(const std::string& upInSensorID, const std::string& upOutSensorID, double& distance);
    bool checkDeviceState();
    void clearCalculateData(const std::string& prefix);
    bool stopCalculate();

private:
    std::atomic_bool m_isCalculating{false};
    std::atomic_bool m_stopCalculate{false};
    int m_calculateCount{3};
    std::string m_inputCurrentStation;
    std::string m_outputCurrentStation;
    std::string m_upCurrentInSensor;
    std::string m_downCurrentInSensor;
    std::string m_upCurrentOutSensor;
    std::string m_downCurrentOutSensor;
    std::mutex m_mutex;
    std::string m_clientID;
    QTimer m_checkTimer;
    QTimer m_updateTimer;
    ZGMqttClient* m_mqttClient{nullptr};
    ZG6000::StringMap m_mapPropertyDataID;
    ZG6000::StringList m_listSensorID;
    std::vector<ZGSTSensor*> m_listSensor;
    std::vector<ZGSTCDCBranch*> m_listCDCBranch;
    bool m_isSyncOperTime{false};
    QTime m_operStartTime;
    QTime m_operEndTime;
    int m_maxWarnHours{3};
    int m_maxCalcDays{3};
    int m_warnOffHours{3};
    int m_autoDrainage{1};

};

#endif // ZGSTSYSTEM_H
