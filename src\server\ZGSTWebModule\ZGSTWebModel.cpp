#include "ZGSTWebModel.h"
#include "ZGSTHandle.h"

ZGSTWebModel::ZGSTWebModel(QObject *parent)
    : ZGWebModule(parent)
{
    ZGSTHandle* pHandle = new ZGSTHandle(this);
    registerHandle("st/stations/get", pHandle, &ZGSTHandle::on_st_stations_get);
    registerHandle("st/station/set", pHandle, &ZGSTHandle::on_st_station_set);
    registerHandle("st/offset/calc", pHandle, &ZGSTHandle::on_st_offset_calc);
    registerHandle("st/calc/start", pHandle, &ZGSTHandle::on_st_calc_start);
    registerHandle("st/calc/stop", pHandle, &ZGSTHandle::on_st_calc_stop);
}

