#############################################################################
# Makefile for building: ZGSTWebModule
# Generated by qmake (3.1) (Qt 6.5.3)
# Project:  ZGSTWebModule.pro
# Template: lib
# Command: E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -o Makefile ZGSTWebModule.pro -spec win32-msvc "CONFIG+=qtquickcompiler"
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: release
install: release-install
uninstall: release-uninstall
QMAKE         = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = E:\Qt\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move
SUBTARGETS    =  \
		release \
		debug


release: $(MAKEFILE) FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release uninstall
debug: $(MAKEFILE) FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug uninstall

Makefile: ZGSTWebModule.pro E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_pre.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-desktop.conf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-vulkan.conf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-desktop.conf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\qconfig.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axbase_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bodymovin_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jsonrpc_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_languageserver_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediaquick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_functions.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_config.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_post.prf \
		.qmake.stash \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-version.conf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\toolchain.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_pre.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\default_pre.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resolve_config.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_post.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qtquickcompiler.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\entrypoint.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\precompile_header.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\warn_on.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources_functions.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\moc.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\opengl.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qmake_use.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\file_copies.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\testcase_targets.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exceptions.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\yacc.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\lex.prf \
		ZGSTWebModule.pro \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Gui.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServer.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSockets.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrent.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.prl \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\build_pass.prf \
		E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\console.prf \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Guid.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServerd.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSocketsd.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Networkd.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrentd.prl \
		E:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored.prl
	$(QMAKE) -o Makefile ZGSTWebModule.pro -spec win32-msvc "CONFIG+=qtquickcompiler"
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_pre.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-desktop.conf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\common\windows-vulkan.conf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-desktop.conf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\qconfig.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3danimation_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dcore_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dextras_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dinput_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dlogic_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickextras_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickinput_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickrender_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_3drender_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_activeqt_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axbase_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axcontainer_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_axserver_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bluetooth_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_bodymovin_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_charts_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_chartsqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualization_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_grpc_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttracker_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jsonrpc_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_languageserver_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_location_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimedia_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediaquick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_networkauth_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_nfc_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdf_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfquick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_pdfwidgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_protobuf_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3d_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3deffects_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick3dutils_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktimeline_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjects_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_repparser_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_scxmlqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensors_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sensorsquick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialbus_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_serialport_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_shadertools_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_spatialaudio_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachine_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_statemachineqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_texttospeech_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webview_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webviewquick_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_functions.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt_config.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\spec_post.prf:
.qmake.stash:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\common\msvc-version.conf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\toolchain.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_pre.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\default_pre.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resolve_config.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\default_post.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qtquickcompiler.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\entrypoint.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\precompile_header.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\warn_on.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qt.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources_functions.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\resources.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\moc.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\opengl.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\qmake_use.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\file_copies.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\windows.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\testcase_targets.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\exceptions.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\yacc.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\lex.prf:
ZGSTWebModule.pro:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Gui.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServer.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSockets.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrent.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.prl:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\build_pass.prf:
E:\Qt\6.5.3\msvc2019_64\mkspecs\features\win32\console.prf:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Guid.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6HttpServerd.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6WebSocketsd.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Networkd.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Concurrentd.prl:
E:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ZGSTWebModule.pro -spec win32-msvc "CONFIG+=qtquickcompiler"

qmake_all: FORCE

make_first: release-make_first debug-make_first  FORCE
all: release-all debug-all  FORCE
clean: release-clean debug-clean  FORCE
	-$(DEL_FILE) ..\..\..\lib\ZGSTWebModule.exp
distclean: release-distclean debug-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash ..\..\..\lib\ZGSTWebModule.lib

release-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocclean
debug-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocclean
mocclean: release-mocclean debug-mocclean

release-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocables
debug-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocables
mocables: release-mocables debug-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Release: Makefile
$(MAKEFILE).Debug: Makefile
